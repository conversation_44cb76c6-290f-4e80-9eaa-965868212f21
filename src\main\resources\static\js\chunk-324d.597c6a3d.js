(window.webpackJsonp=window.webpackJsonp||[]).push([["chunk-324d"],{"r/12":function(t,e,a){},re1V:function(t,e,a){"use strict";var r=a("r/12");a.n(r).a},xjht:function(t,e,a){"use strict";a.d(e,"a",function(){return n}),a.d(e,"b",function(){return i}),a.d(e,"c",function(){return o}),a.d(e,"e",function(){return s}),a.d(e,"f",function(){return l}),a.d(e,"d",function(){return h});var r=a("t3Un");function n(t){return Object(r.a)({url:"/chart/getEmpChart",method:"get",params:{date:t}})}function i(t,e){return Object(r.a)({url:"/chart/getEmpSalesChart",method:"get",params:{id:t,date:e}})}function o(){return Object(r.a)({url:"/chart/getIndexChart",method:"get"})}function s(){return Object(r.a)({url:"/chart/getSaleNum",method:"get"})}function l(t,e){return Object(r.a)({url:"/chart/getSalesChart",method:"get",params:{start:t,end:e}})}function h(t){return Object(r.a)({url:"/chart/getIndexSales",method:"get",params:{id:t}})}},yhiS:function(t,e,a){"use strict";a.r(e);var r=a("rerW"),n=a.n(r),i=a("6ZY3"),o=a.n(i),s=a("88Rz"),l=a.n(s),h=a("7Qib"),c={props:{className:{type:String,default:"chart"},id:{type:String,default:"chart"},width:{type:String,default:"200px"},height:{type:String,default:"200px"},option:{type:Object,default:null}},data:function(){return{myChart:null}},computed:{opt:function(){return{backgroundColor:"#344b58",title:{text:this.option.start+"-"+this.option.end+" 销量报表",x:"20",top:"20",textStyle:{color:"#fff",fontSize:"22"},subtextStyle:{color:"#90979c",fontSize:"16"}},tooltip:{trigger:"axis",axisPointer:{textStyle:{color:"#fff"}}},toolbox:{show:!0,feature:{mark:{show:!0},dataView:{show:!0,readOnly:!1},magicType:{show:!0,type:["line","bar","stack","tiled"]},restore:{show:!0},saveAsImage:{show:!0}}},grid:{left:"5%",right:"5%",borderWidth:0,top:150,bottom:95,textStyle:{color:"#fff"}},legend:{x:"5%",top:"10%",textStyle:{color:"#90979c"},data:["收入","支出","利润"]},calculable:!0,xAxis:[{type:"category",axisLine:{lineStyle:{color:"#90979c"}},splitLine:{show:!1},axisTick:{show:!1},splitArea:{show:!1},axisLabel:{interval:0},data:this.option.xAxis.data}],yAxis:[{type:"value",name:"支出 & 收入",splitLine:{show:!1},axisLine:{lineStyle:{formatter:"{value} W",color:"#90979c"}},axisTick:{show:!1},axisLabel:{interval:0},splitArea:{show:!1}},{type:"value",name:"利润",splitLine:{show:!1},axisLine:{lineStyle:{formatter:"{value} W",color:"#90979c"}},axisTick:{show:!1},axisLabel:{interval:0},splitArea:{show:!1}}],series:[{name:"利润",type:"line",symbolSize:10,symbol:"circle",yAxisIndex:1,itemStyle:{normal:{color:"rgba(252,230,48,1)",barBorderRadius:0,label:{show:!0,position:"top",formatter:function(t){return t.value>0?t.value:""}}}},data:this.option.series[2].data},{name:"收入",type:"bar",barMaxWidth:35,barGap:"10%",itemStyle:{normal:{color:"rgba(255,144,128,1)",label:{show:!0,position:"top",textStyle:{color:"#fff"},formatter:function(t){return t.value>0?t.value:""}}}},data:this.option.series[0].data},{name:"支出",type:"bar",barMaxWidth:35,barGap:"10%",itemStyle:{normal:{color:"rgba(0,191,183,1)",barBorderRadius:0,label:{show:!0,position:"top",formatter:function(t){return t.value>0?t.value:""}}}},data:this.option.series[1].data}]}}},watch:{option:function(){this.chartChange()}},updated:function(){this.myChart||this.initChart(),this.ChartChange()},mounted:function(){var t=this;this.initChart(),this.__resizeHandler=Object(h.a)(function(){t.myChart&&t.myChart.resize()},100),window.addEventListener("resize",this.__resizeHandler)},beforeDestroy:function(){this.myChart&&(window.removeEventListener("resize",this.__resizeHandler),this.myChart.dispose(),this.myChart=null)},methods:{initChart:function(){this.myChart=l.a.init(document.getElementById(this.id)),this.myChart.setOption(this.opt)},chartChange:function(){this.myChart.setOption(this.opt)}}},d=a("ZrdR"),u=Object(d.a)(c,function(){var t=this.$createElement;return(this._self._c||t)("div",{class:this.className,style:{height:this.height,width:this.width},attrs:{id:this.id}})},[],!1,null,null,null);u.options.__file="mixChart.vue";var p=u.exports,f=a("xjht"),m={name:"MixChart",components:{Chart:p},data:function(){var t=this;return{start:null,end:null,startPicker:{disabledDate:function(t){return t.getTime()>Date.now()}},endPicker:{disabledDate:function(e){return null!=t.start?e.getTime()>Date.now()||e.getTime()<t.start:e.getTime()>Date.now()}},option:{start:void 0,end:void 0,xAxis:{data:[]},series:[{data:[]},{data:[]},{data:[]}]}}},created:function(){this.initDate()},methods:{initDate:function(){var t=new Date;t.setFullYear(t.getFullYear()-1),this.start=Object(h.d)(t,"{y}-{m}"),0===t.getMonth()?(t.setMonth(11),this.end=Object(h.d)(t,"{y}-{m}")):((t=new Date).setMonth(t.getMonth()-1),this.end=Object(h.d)(t,"{y}-{m}")),this.changeChart()},changeChart:function(){var t=this;null!==this.start&&null!==this.end&&Object(f.f)(this.start,this.end).then(function(e){if(2e4===e.data.code){var a=o()({},t.option),r=e.data.data;console.log(r);var i=[],s=[{data:[]},{data:[]},{data:[]}],l=!0,c=!1,d=void 0;try{for(var u,p=n()(r);!(l=(u=p.next()).done);l=!0){var f=u.value;i.push(f.date),s[0].data.push(f.income),s[1].data.push(f.expenditure),s[2].data.push(f.profit)}}catch(t){c=!0,d=t}finally{try{!l&&p.return&&p.return()}finally{if(c)throw d}}a.start=Object(h.d)(t.start,"{y}年{m}月"),a.end=Object(h.d)(t.end,"{y}年{m}月"),a.xAxis.data=i,a.series=s,t.option=a}else t.$notify({title:"错误",message:e.data.message,type:"error",duration:2e3})})},getMonthAll:function(t,e){var a=e,r=[],n=t.split("-"),i=a.split("-"),o=0;if((o=parseInt(n[0])<parseInt(i[0])?12*(parseInt(i[0])-parseInt(n[0]))+parseInt(i[1])-parseInt(n[1])+1:parseInt(i[1])-parseInt(n[1])+1)>0)for(var s=parseInt(n[1]),l=parseInt(n[0]),h=0;h<o;h++)s<12?(r[h]=l+"年"+(s>9?s:"0"+s)+"月",s+=1):(r[h]=l+"年"+(s>9?s:"0"+s)+"月",s=1,l+=1);return r}}},y=(a("re1V"),Object(d.a)(m,function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"chart-container"},[a("chart",{attrs:{option:t.option,height:"100%",width:"100%"}}),t._v(" "),a("el-form",{ref:"form",staticClass:"month-select",attrs:{"label-width":"100px",size:"mini"}},[a("el-form-item",{attrs:{label:"时间"}},[a("el-row",{attrs:{gutter:24}},[a("el-col",{attrs:{span:10}},[a("el-date-picker",{attrs:{"picker-options":t.startPicker,"value-format":"yyyy-MM",type:"month",placeholder:"开始时间"},model:{value:t.start,callback:function(e){t.start=e},expression:"start"}})],1),t._v(" "),a("el-col",{attrs:{span:10}},[a("el-date-picker",{attrs:{"picker-options":t.endPicker,"value-format":"yyyy-MM",type:"month",placeholder:"结束时间"},model:{value:t.end,callback:function(e){t.end=e},expression:"end"}})],1),t._v(" "),a("el-col",{attrs:{span:2}},[a("el-button",{attrs:{type:"primary"},on:{click:t.changeChart}},[t._v("确定")])],1)],1)],1)],1)],1)},[],!1,null,null,null));y.options.__file="sales.vue";e.default=y.exports}}]);