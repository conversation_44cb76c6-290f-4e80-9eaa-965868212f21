(window.webpackJsonp=window.webpackJsonp||[]).push([["chunk-df41"],{"+Lc1":function(e,t,r){"use strict";r.d(t,"a",function(){return s}),r.d(t,"d",function(){return a}),r.d(t,"f",function(){return l}),r.d(t,"e",function(){return u}),r.d(t,"b",function(){return c}),r.d(t,"c",function(){return d});var o=r("omC7"),n=r.n(o),i=r("t3Un");function s(e){return Object(i.a)({headers:{Accept:"application/json","Content-Type":"application/json"},url:"/order/addOrder",method:"post",data:n()(e)})}function a(e){return Object(i.a)({url:"/order/getList",method:"get",params:e})}function l(e){return Object(i.a)({url:"/order/update",method:"post",params:e})}function u(e){return Object(i.a)({url:"/order/updateDetail",method:"post",params:e})}function c(e){return Object(i.a)({url:"/order/deleteDetail",method:"post",params:e})}function d(e){return Object(i.a)({url:"/order/getDetailsList",method:"get",params:e})}},"3KUV":function(e,t,r){"use strict";r.d(t,"c",function(){return n}),r.d(t,"a",function(){return i}),r.d(t,"d",function(){return s}),r.d(t,"b",function(){return a});var o=r("t3Un");function n(){return Object(o.a)({url:"/init/seriesOpt",method:"get"})}function i(){return Object(o.a)({url:"/init/brandOpt",method:"get"})}function s(e){return Object(o.a)({url:"/init/storeOpt",method:"get",params:{seriesId:e}})}function a(e){return Object(o.a)({url:"/init/getCustomer",method:"get",params:{idCard:e}})}},"69Nf":function(e,t,r){var o=r("f4ri"),n=o.JSON||(o.JSON={stringify:JSON.stringify});e.exports=function(e){return n.stringify.apply(n,arguments)}},Bfuw:function(e,t,r){var o=r("XdqN"),n=Math.floor;e.exports=function(e){return!o(e)&&isFinite(e)&&n(e)===e}},SjsA:function(e,t,r){"use strict";r.r(t);var o=r("6ZY3"),n=r.n(o),i=r("Yfch"),s=r("3KUV"),a=r("+Lc1"),l=[{key:"0",display_name:"未支付"},{key:"1",display_name:"已支付"}],u={data:function(){return{ruleForm:{idCard:void 0,customer:{id:void 0,name:""},status:"0",totalPrice:null,domains:[{storeIndex:null,carId:null,carNumber:0,salePrice:0,selectedOptions:[],store:null}]},rules:{status:[{required:!0,message:"请选择支付状态",trigger:"blur"}],idCard:[{required:!0,validator:i.c,trigger:"blur"}]},orderStatusOptions:l,options:[],commitAble:!1}},created:function(){this.getSeriesOpt()},methods:{getSeriesOpt:function(){var e=this;Object(s.c)().then(function(t){e.options=t.data.data})},getStore:function(e){var t=this;Object(s.d)(this.ruleForm.domains[e].selectedOptions[1]).then(function(r){t.ruleForm.domains[e].store=r.data.data,t.ruleForm.domains[e].carId=null,t.ruleForm.domains[e].storeIndex=null})},getPrice:function(e){var t=this.ruleForm.domains[e].storeIndex;this.ruleForm.domains[e].salePrice=this.ruleForm.domains[e].store[t].salePrice,this.ruleForm.domains[e].carId=this.ruleForm.domains[e].store[t].id,this.getTotalPrice()},getTotalPrice:function(){for(var e=this.ruleForm.domains,t=0,r=0;r<e.length&&e[r];r++)t+=e[r].salePrice*e[r].carNumber;this.ruleForm.totalPrice=t},getCustomer:function(){var e=this;this.resetCustomer(),this.ruleForm.customerId=null,Object(i.a)(this.ruleForm.idCard)&&Object(s.b)(this.ruleForm.idCard).then(function(t){2e4===t.data.code?(e.ruleForm.customer=t.data.data,e.commitAble=!0):(e.commitAble=!1,e.ruleForm.customer.name="<small style='color: red'>"+t.data.message+"</small>")})},resetRuleForm:function(){this.ruleForm={idCard:void 0,customer:{id:void 0,name:""},status:"0",totalPrice:null,domains:[{storeIndex:null,carId:null,carNumber:0,salePrice:0,selectedOptions:[],store:null}]}},resetCustomer:function(){this.ruleForm.customer={id:void 0,name:""}},submitForm:function(){var e=this;this.$refs.ruleForm.validate(function(t){if(!t||!e.commitAble)return console.log("error submit!!"),!1;var r=n()({},e.ruleForm),o=e.$store.getters.id;null===o&&""===o&&e.$message({message:"登录信息有误，请重新登录!",type:"error"}),Object(a.a)({employeeId:o,customerId:r.customer.id,status:r.status,totalPrice:r.totalPrice,detailVos:r.domains}).then(function(t){2e4===t.data.code?(e.$notify({title:"成功",message:"添加成功",type:"success",duration:2e3}),e.resetForm("ruleForm")):e.$notify({title:"错误",message:t.data.message,type:"error",duration:2e3})})})},resetForm:function(e){this.resetRuleForm(),this.$refs[e].resetFields()},removeDomain:function(e){var t=this.ruleForm.domains.indexOf(e);0!==t&&this.ruleForm.domains.splice(t,1)},addDomain:function(){this.ruleForm.domains.push({storeIndex:null,carId:null,carNumber:0,salePrice:0,selectedOptions:[],store:null,key:Date.now()})}}},c=r("ZrdR"),d=Object(c.a)(u,function(){var e=this,t=e.$createElement,r=e._self._c||t;return r("div",{staticClass:"app-container"},[r("el-header",[r("h3",{staticStyle:{"font-family":"Microsoft YaHei"}},[e._v("新订单")]),e._v(" "),r("hr",{staticStyle:{border:"0.5px solid #dcdfe6"}})]),e._v(" "),r("el-form",{ref:"ruleForm",staticClass:"demo-ruleForm",staticStyle:{width:"500px","margin-left":"50px"},attrs:{model:e.ruleForm,rules:e.rules,"label-width":"130px"}},[r("el-form-item",{attrs:{label:"客户身份证号",prop:"idCard"}},[r("el-input",{on:{change:e.getCustomer},model:{value:e.ruleForm.idCard,callback:function(t){e.$set(e.ruleForm,"idCard",t)},expression:"ruleForm.idCard"}})],1),e._v(" "),r("el-form-item",{attrs:{label:"客户姓名"}},[r("span",{staticStyle:{"margin-left":"15px"},domProps:{innerHTML:e._s(e.ruleForm.customer.name)}})]),e._v(" "),r("el-form-item",{attrs:{label:"支付状态",prop:"status"}},[r("el-select",{staticClass:"filter-item",attrs:{placeholder:"Please select"},model:{value:e.ruleForm.status,callback:function(t){e.$set(e.ruleForm,"status",t)},expression:"ruleForm.status"}},e._l(e.orderStatusOptions,function(e){return r("el-option",{key:e.key,attrs:{label:e.display_name,value:e.key}})}))],1),e._v(" "),e._l(e.ruleForm.domains,function(t,o){return r("div",{key:t.key},[r("hr",{staticStyle:{border:"0.5px solid #dcdfe6"}}),e._v(" "),r("el-form-item",[r("el-button",{staticClass:"el-icon-remove-outline",on:{click:function(r){r.preventDefault(),e.removeDomain(t)}}},[e._v(" 撤销")])],1),e._v(" "),r("el-form-item",{attrs:{rules:{required:!0,message:"必须选择车系",trigger:"change"},prop:"domains."+o+".selectedOptions",label:"品牌 & 车系"}},[r("el-cascader",{attrs:{options:e.options,placeholder:"品牌/车系"},on:{change:function(t){e.getStore(o)}},model:{value:t.selectedOptions,callback:function(r){e.$set(t,"selectedOptions",r)},expression:"domain.selectedOptions"}})],1),e._v(" "),r("el-form-item",{attrs:{rules:{required:!0,message:"必须选择型号",trigger:"change"},prop:"domains."+o+".carId",label:"型号 & 颜色"}},[r("el-select",{staticClass:"filter-item",staticStyle:{width:"100%"},attrs:{placeholder:"Please select"},on:{change:function(t){e.getPrice(o)}},model:{value:t.storeIndex,callback:function(r){e.$set(t,"storeIndex",r)},expression:"domain.storeIndex"}},e._l(t.store,function(t,o){return r("el-option",{key:t.id,attrs:{label:t.type+" "+t.color,value:o}},[r("span",[e._v(e._s(t.type))]),e._v(" "),r("el-tag",[e._v(e._s(t.color))])],1)}))],1),e._v(" "),r("el-form-item",{attrs:{rules:{required:!0,message:"必须填入数量",trigger:"blur"},prop:"domains."+o+".carNumber",label:"数量"}},[r("el-input-number",{attrs:{min:1},on:{change:e.getTotalPrice},model:{value:t.carNumber,callback:function(r){e.$set(t,"carNumber",r)},expression:"domain.carNumber"}})],1),e._v(" "),r("el-form-item",{attrs:{label:"售价"}},[null!==t.storeIndex?r("span",{staticStyle:{"margin-left":"15px"}},[e._v(e._s(t.salePrice)+" 元/辆")]):e._e()])],1)}),e._v(" "),r("el-form-item",[r("el-button",{staticClass:"el-icon-circle-plus-outline",on:{click:e.addDomain}},[e._v(" 添加车辆")])],1),e._v(" "),r("hr",{staticStyle:{border:"0.5px solid #dcdfe6"}}),e._v(" "),r("el-form-item",{attrs:{label:"总价"}},[r("span",{staticStyle:{"margin-left":"15px"}},[e._v(e._s(e.ruleForm.totalPrice))]),e._v(" 元\n    ")]),e._v(" "),r("el-form-item",[r("el-button",{attrs:{type:"primary"},on:{click:e.submitForm}},[e._v("提交")]),e._v(" "),r("el-button",{on:{click:function(t){e.resetForm("ruleForm")}}},[e._v("重置")])],1)],2)],1)},[],!1,null,null,null);d.options.__file="addOrder.vue";t.default=d.exports},Yfch:function(e,t,r){"use strict";r.d(t,"a",function(){return i}),r.d(t,"c",function(){return s}),r.d(t,"b",function(){return a}),r.d(t,"d",function(){return l});var o=r("e9eg"),n=r.n(o);function i(e){return/^[1-9]\d{5}(18|19|([23]\d))\d{2}((0[1-9])|(10|11|12))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/.test(e)}var s=function(e,t,r){if(""===t||void 0===t)r(new Error("请输入身份证号"));else{/^[1-9]\d{5}(18|19|([23]\d))\d{2}((0[1-9])|(10|11|12))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/.test(t)||r(new Error("请输入正确的身份证号")),r()}},a=function(e,t,r){if(!t)return r(new Error("年龄不能为空"));setTimeout(function(){n()(t)?t<18?r(new Error("必须年满18岁")):r():r(new Error("请输入数字值"))},1e3)},l=function(e,t,r){if(!t)return r(new Error("请输入员工薪资"));setTimeout(function(){n()(t)?t<1800?r(new Error("薪资必须大于1800元")):r():r(new Error("请输入数字值"))},1e3)}},e9eg:function(e,t,r){e.exports={default:r("t3Qm"),__esModule:!0}},kmh5:function(e,t,r){var o=r("NCe+");o(o.S,"Number",{isInteger:r("Bfuw")})},omC7:function(e,t,r){e.exports={default:r("69Nf"),__esModule:!0}},t3Qm:function(e,t,r){r("kmh5"),e.exports=r("f4ri").Number.isInteger}}]);