(window.webpackJsonp=window.webpackJsonp||[]).push([["chunk-720a"],{"1Bvf":function(e,t,i){},"3KUV":function(e,t,i){"use strict";i.d(t,"c",function(){return r}),i.d(t,"a",function(){return a}),i.d(t,"d",function(){return s}),i.d(t,"b",function(){return l});var n=i("t3Un");function r(){return Object(n.a)({url:"/init/seriesOpt",method:"get"})}function a(){return Object(n.a)({url:"/init/brandOpt",method:"get"})}function s(e){return Object(n.a)({url:"/init/storeOpt",method:"get",params:{seriesId:e}})}function l(e){return Object(n.a)({url:"/init/getCustomer",method:"get",params:{idCard:e}})}},"K/D6":function(e,t,i){"use strict";var n=i("1Bvf");i.n(n).a},Mz3J:function(e,t,i){"use strict";Math.easeInOutQuad=function(e,t,i,n){return(e/=n/2)<1?i/2*e*e+t:-i/2*(--e*(e-2)-1)+t};var n=window.requestAnimationFrame||window.webkitRequestAnimationFrame||window.mozRequestAnimationFrame||function(e){window.setTimeout(e,1e3/60)};function r(e,t,i){var r=document.documentElement.scrollTop||document.body.parentNode.scrollTop||document.body.scrollTop,a=e-r,s=0;t=void 0===t?500:t;!function e(){s+=20,function(e){document.documentElement.scrollTop=e,document.body.parentNode.scrollTop=e,document.body.scrollTop=e}(Math.easeInOutQuad(s,r,a,t)),s<t?n(e):i&&"function"==typeof i&&i()}()}var a={name:"Pagination",props:{total:{required:!0,type:Number},page:{type:Number,default:1},limit:{type:Number,default:5},pageSizes:{type:Array,default:function(){return[5,10,20,50]}},layout:{type:String,default:"total, sizes, prev, pager, next, jumper"},background:{type:Boolean,default:!0},autoScroll:{type:Boolean,default:!0},hidden:{type:Boolean,default:!1}},computed:{currentPage:{get:function(){return this.page},set:function(e){this.$emit("update:page",e)}},pageSize:{get:function(){return this.limit},set:function(e){this.$emit("update:limit",e)}}},methods:{handleSizeChange:function(e){this.$emit("pagination",{page:this.currentPage,limit:e}),this.autoScroll&&r(0,800)},handleCurrentChange:function(e){this.$emit("pagination",{page:e,limit:this.pageSize}),this.autoScroll&&r(0,800)}}},s=(i("K/D6"),i("ZrdR")),l=Object(s.a)(a,function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("div",{staticClass:"pagination-container",class:{hidden:e.hidden}},[i("el-pagination",e._b({attrs:{background:e.background,"current-page":e.currentPage,"page-size":e.pageSize,layout:e.layout,"page-sizes":e.pageSizes,total:e.total},on:{"update:currentPage":function(t){e.currentPage=t},"update:pageSize":function(t){e.pageSize=t},"size-change":e.handleSizeChange,"current-change":e.handleCurrentChange}},"el-pagination",e.$attrs,!1))],1)},[],!1,null,"6fb7ae22",null);l.options.__file="index.vue";t.a=l.exports},ZySA:function(e,t,i){"use strict";var n=i("6ZY3"),r=i.n(n),a=(i("jUE0"),{bind:function(e,t){e.addEventListener("click",function(i){var n=r()({},t.value),a=r()({ele:e,type:"hit",color:"rgba(0, 0, 0, 0.15)"},n),s=a.ele;if(s){s.style.position="relative",s.style.overflow="hidden";var l=s.getBoundingClientRect(),o=s.querySelector(".waves-ripple");switch(o?o.className="waves-ripple":((o=document.createElement("span")).className="waves-ripple",o.style.height=o.style.width=Math.max(l.width,l.height)+"px",s.appendChild(o)),a.type){case"center":o.style.top=l.height/2-o.offsetHeight/2+"px",o.style.left=l.width/2-o.offsetWidth/2+"px";break;default:o.style.top=(i.pageY-l.top-o.offsetHeight/2-document.documentElement.scrollTop||document.body.scrollTop)+"px",o.style.left=(i.pageX-l.left-o.offsetWidth/2-document.documentElement.scrollLeft||document.body.scrollLeft)+"px"}return o.style.backgroundColor=a.color,o.className="waves-ripple z-active",!1}},!1)}}),s=function(e){e.directive("waves",a)};window.Vue&&(window.waves=a,Vue.use(s)),a.install=s;t.a=a},"c/X4":function(e,t,i){"use strict";i.d(t,"a",function(){return r}),i.d(t,"d",function(){return a}),i.d(t,"b",function(){return s}),i.d(t,"e",function(){return l}),i.d(t,"c",function(){return o}),i.d(t,"f",function(){return u}),i.d(t,"g",function(){return c});var n=i("t3Un");function r(e){return Object(n.a)({url:"/store/addBrand",method:"get",params:{brand:e}})}function a(e){return Object(n.a)({url:"/store/delBrand",method:"get",params:{brandId:e}})}function s(e,t){return Object(n.a)({url:"/store/addSeries",method:"get",params:{brandId:e,seriesName:t}})}function l(e){return Object(n.a)({url:"/store/delSeries",method:"get",params:{seriesId:e}})}function o(e){return Object(n.a)({url:"/store/addStore",method:"post",params:e})}function u(e){return Object(n.a)({url:"/store/getList",method:"get",params:e})}function c(e){return console.log(e),Object(n.a)({url:"/store/update",method:"post",params:e})}},jUE0:function(e,t,i){},urAg:function(e,t,i){"use strict";i.r(t);var n=i("6ZY3"),r=i.n(n),a=i("rerW"),s=i.n(a),l=i("ZySA"),o=i("7Qib"),u=i("Mz3J"),c=i("Q2AE");var d=i("3KUV"),p=i("c/X4"),m=[{key:"0",display_name:"停售"},{key:"1",display_name:"在售"},{key:"2",display_name:"缺货"}],f=m.reduce(function(e,t){return e[t.key]=t.display_name,e},{}),h={components:{Pagination:u.a},directives:{waves:l.a},filters:{statusFilter:function(e){return{0:"info",1:"success",2:"danger"}[e]},typeFilter:function(e){return f[e]}},data:function(){return{tableKey:0,list:null,total:0,listLoading:!0,listQuery:{page:1,limit:5,id:void 0,brandId:void 0,seriesId:void 0,status:void 0,orderBy:void 0},carStatusOptions:m,temp:{id:void 0,series:[],seriesId:void 0,type:"",color:"",price:void 0,salePrice:void 0,repertory:void 0,status:void 0},dialogFormVisible:!1,options:[],seriesOptions:[],rules:{seriesId:[{required:!0,message:"请输入品牌 & 车系"}],type:[{required:!0,message:"请输入车辆型号",trigger:"blur"}],color:[{required:!0,message:"请输入车辆颜色",trigger:"blur"}],price:[{required:!0,message:"请输入车辆进价",trigger:"blur"},{type:"number",message:"进价必须为数字值"}],salePrice:[{required:!0,message:"请输入车辆售价",trigger:"blur"},{type:"number",message:"售价必须为数字值"}],repertory:[{required:!0,message:"请输入车辆库存"}]},downloadLoading:!1}},created:function(){this.getList(),this.getSeriesOpt()},methods:{getSeriesOpt:function(){var e=this;Object(d.c)().then(function(t){e.options=t.data.data})},getBrandOpt:function(){var e=this;Object(d.a)().then(function(t){e.brandOptions=t.data.data})},checkPermission:function(e){if(e&&e instanceof Array&&e.length>0){var t=e;return!!(c.a.getters&&c.a.getters.roles).some(function(e){return t.includes(e)})}return console.error("need roles! Like v-permission=\"['admin','editor']\""),!1},getList:function(){var e=this;this.listLoading=!0,Object(p.f)(this.listQuery).then(function(t){2e4===t.data.code?(e.list=t.data.data.items,e.total=t.data.data.total):e.$message({message:t.data.message,type:"error"}),e.listLoading=!1})},changeSeries:function(){this.listQuery.seriesId=null;var e=this.listQuery.brandId;if(""===e||null===e)this.seriesOptions=[];else{var t=void 0,i=!0,n=!1,r=void 0;try{for(var a,l=s()(this.options);!(i=(a=l.next()).done);i=!0){var o=a.value;if(o.value===e){t=this.options.indexOf(o);break}}}catch(e){n=!0,r=e}finally{try{!i&&l.return&&l.return()}finally{if(n)throw r}}this.seriesOptions=this.options[t].children}},handleFilter:function(){this.listQuery.page=1,this.getList()},sortChange:function(e){var t=e.prop,i=e.order;this.listQuery.orderBy="ascending"===i?t+" asc":t+" desc",this.handleFilter()},handleUpdate:function(e){var t=this;this.temp=r()({},e),this.temp.series=[e.brandId,e.seriesId],this.dialogFormVisible=!0,this.$nextTick(function(){t.$refs.dataForm.clearValidate()})},updateData:function(){var e=this;this.$refs.dataForm.validate(function(t){if(t){var i=r()({},e.temp);i.seriesId=e.temp.series[1],i.createTime=null,i.series=null,Object(p.g)(i).then(function(t){if(2e4===t.data.code){if(i.seriesId===e.temp.seriesId){var n=!0,r=!1,a=void 0;try{for(var l,o=s()(e.list);!(n=(l=o.next()).done);n=!0){var u=l.value;if(u.id===e.temp.id){var c=e.list.indexOf(u);e.list.splice(c,1,e.temp);break}}}catch(e){r=!0,a=e}finally{try{!n&&o.return&&o.return()}finally{if(r)throw a}}}else e.getList();e.$notify({title:"成功",message:"更新成功",type:"success",duration:2e3})}else e.$notify({title:"错误",message:t.data.message,type:"error",duration:2e3});e.dialogFormVisible=!1})}})},handleDownload:function(){var e=this;this.downloadLoading=!0,Promise.all([i.e("chunk-3806"),i.e("chunk-5556")]).then(i.bind(null,"S/jZ")).then(function(t){var i=e.formatJson(["id","brandName","seriesName","type","color","price","salePrice","createTime","status"],e.list);t.export_json_to_excel({header:["库存编号","品牌","车系","型号","颜色","进价","售价","入库时间","状态"],data:i,filename:"库存信息"}),e.downloadLoading=!1})},formatJson:function(e,t){return t.map(function(t){return e.map(function(e){return"createTime"===e?Object(o.d)(t[e]):"status"===e?f[t[e]]:t[e]})})}}},v=i("ZrdR"),y=Object(v.a)(h,function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("div",{staticClass:"app-container"},[i("div",{staticClass:"filter-container"},[i("el-input",{staticClass:"filter-item",staticStyle:{width:"160px"},attrs:{placeholder:"车辆编号",clearable:""},nativeOn:{keyup:function(t){return"button"in t||!e._k(t.keyCode,"enter",13,t.key,"Enter")?e.handleFilter(t):null}},model:{value:e.listQuery.id,callback:function(t){e.$set(e.listQuery,"id",t)},expression:"listQuery.id"}}),e._v(" "),i("el-select",{staticClass:"filter-item",staticStyle:{width:"160px"},attrs:{clearable:"",placeholder:"品牌"},on:{change:e.changeSeries},nativeOn:{keyup:function(t){return"button"in t||!e._k(t.keyCode,"enter",13,t.key,"Enter")?e.handleFilter(t):null}},model:{value:e.listQuery.brandId,callback:function(t){e.$set(e.listQuery,"brandId",t)},expression:"listQuery.brandId"}},e._l(e.options,function(e){return i("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),e._v(" "),i("el-select",{staticClass:"filter-item",staticStyle:{width:"160px"},attrs:{clearable:"",placeholder:"车系"},nativeOn:{keyup:function(t){return"button"in t||!e._k(t.keyCode,"enter",13,t.key,"Enter")?e.handleFilter(t):null}},model:{value:e.listQuery.seriesId,callback:function(t){e.$set(e.listQuery,"seriesId",t)},expression:"listQuery.seriesId"}},e._l(e.seriesOptions,function(e){return i("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),e._v(" "),i("el-select",{staticClass:"filter-item",staticStyle:{width:"110px"},attrs:{placeholder:"状态",clearable:""},nativeOn:{keyup:function(t){return"button"in t||!e._k(t.keyCode,"enter",13,t.key,"Enter")?e.handleFilter(t):null}},model:{value:e.listQuery.status,callback:function(t){e.$set(e.listQuery,"status",t)},expression:"listQuery.status"}},e._l(e.carStatusOptions,function(e){return i("el-option",{key:e.key,attrs:{label:e.display_name,value:e.key}})})),e._v(" "),i("el-button",{directives:[{name:"waves",rawName:"v-waves"}],staticClass:"filter-item",attrs:{type:"primary",icon:"el-icon-search"},on:{click:e.handleFilter}},[e._v("搜索")]),e._v(" "),i("el-button",{directives:[{name:"waves",rawName:"v-waves"}],staticClass:"filter-item",attrs:{loading:e.downloadLoading,type:"primary",icon:"el-icon-download"},on:{click:e.handleDownload}},[e._v("导出")])],1),e._v(" "),i("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.listLoading,expression:"listLoading"}],key:e.tableKey,staticStyle:{width:"100%"},attrs:{data:e.list,fit:"","highlight-current-row":""},on:{"sort-change":e.sortChange}},[i("el-table-column",{attrs:{label:"车辆编号",prop:"id",sortable:"custom",align:"center","min-width":"105px"},scopedSlots:e._u([{key:"default",fn:function(t){return[i("span",[e._v(e._s(t.row.id))])]}}])}),e._v(" "),i("el-table-column",{attrs:{label:"品牌/车系/型号",align:"center","min-width":"215px"},scopedSlots:e._u([{key:"default",fn:function(t){return[i("span",[e._v(e._s(t.row.brandName)+"/"+e._s(t.row.seriesName))]),i("br"),e._v(" "),i("span",[e._v(e._s(t.row.type))])]}}])}),e._v(" "),i("el-table-column",{attrs:{label:"颜色",align:"center","min-width":"75px"},scopedSlots:e._u([{key:"default",fn:function(t){return[i("span",[e._v(e._s(t.row.color))])]}}])}),e._v(" "),i("el-table-column",{attrs:{label:"进价",sortable:"custom",prop:"price",align:"center","min-width":"105px"},scopedSlots:e._u([{key:"default",fn:function(t){return[i("span",[e._v("￥ "+e._s(t.row.price))])]}}])}),e._v(" "),i("el-table-column",{attrs:{label:"售价",sortable:"custom",prop:"salePrice",align:"center","min-width":"105px"},scopedSlots:e._u([{key:"default",fn:function(t){return[i("span",[e._v("￥ "+e._s(t.row.salePrice))])]}}])}),e._v(" "),i("el-table-column",{attrs:{label:"库存数",prop:"repertory","min-width":"85px",sortable:"custom",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[i("span",[e._v(e._s(t.row.repertory))])]}}])}),e._v(" "),i("el-table-column",{attrs:{label:"入库时间",prop:"createTime",sortable:"custom","min-width":"135px",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[i("span",[e._v(e._s(e._f("parseTime")(t.row.createTime,"{y}-{m}-{d} {h}:{i}")))])]}}])}),e._v(" "),i("el-table-column",{attrs:{label:"状态","class-name":"status-col","min-width":"90px"},scopedSlots:e._u([{key:"default",fn:function(t){return[i("el-tag",{attrs:{type:e._f("statusFilter")(t.row.status)}},[e._v(e._s(e._f("typeFilter")(t.row.status)))])]}}])}),e._v(" "),e.checkPermission(["admin"])?i("el-table-column",{attrs:{label:"操作",align:"center","min-width":"80px"},scopedSlots:e._u([{key:"default",fn:function(t){return[i("el-button",{attrs:{type:"primary",size:"mini"},on:{click:function(i){e.handleUpdate(t.row)}}},[e._v("编辑")])]}}])}):e._e()],1),e._v(" "),i("pagination",{directives:[{name:"show",rawName:"v-show",value:e.total>0,expression:"total>0"}],attrs:{total:e.total,page:e.listQuery.page,limit:e.listQuery.limit},on:{"update:page":function(t){e.$set(e.listQuery,"page",t)},"update:limit":function(t){e.$set(e.listQuery,"limit",t)},pagination:e.getList}}),e._v(" "),e.checkPermission(["admin"])?i("el-dialog",{attrs:{visible:e.dialogFormVisible,title:"修改库存信息"},on:{"update:visible":function(t){e.dialogFormVisible=t}}},[i("el-form",{ref:"dataForm",staticStyle:{width:"400px","margin-left":"50px"},attrs:{rules:e.rules,model:e.temp,"label-position":"right","label-width":"100px"}},[i("el-form-item",{attrs:{label:"品牌 & 车系",prop:"series"}},[i("el-cascader",{staticStyle:{width:"220px"},attrs:{options:e.options,placeholder:"品牌/车系"},model:{value:e.temp.series,callback:function(t){e.$set(e.temp,"series",t)},expression:"temp.series"}})],1),e._v(" "),i("el-form-item",{attrs:{label:"型号",prop:"type"}},[i("el-input",{model:{value:e.temp.type,callback:function(t){e.$set(e.temp,"type",t)},expression:"temp.type"}})],1),e._v(" "),i("el-form-item",{attrs:{label:"颜色",prop:"color"}},[i("el-input",{model:{value:e.temp.color,callback:function(t){e.$set(e.temp,"color",t)},expression:"temp.color"}})],1),e._v(" "),i("el-form-item",{attrs:{label:"进价",prop:"price"}},[i("el-input",{model:{value:e.temp.price,callback:function(t){e.$set(e.temp,"price",e._n(t))},expression:"temp.price"}})],1),e._v(" "),i("el-form-item",{attrs:{label:"售价",prop:"salePrice"}},[i("el-input",{model:{value:e.temp.salePrice,callback:function(t){e.$set(e.temp,"salePrice",e._n(t))},expression:"temp.salePrice"}})],1),e._v(" "),i("el-form-item",{attrs:{label:"数量",prop:"repertory"}},[i("el-input-number",{attrs:{min:0},model:{value:e.temp.repertory,callback:function(t){e.$set(e.temp,"repertory",t)},expression:"temp.repertory"}})],1),e._v(" "),i("el-form-item",{attrs:{label:"状态",required:""}},[i("el-select",{staticClass:"filter-item",attrs:{placeholder:"Please select"},model:{value:e.temp.status,callback:function(t){e.$set(e.temp,"status",t)},expression:"temp.status"}},e._l(e.carStatusOptions,function(e){return i("el-option",{key:e.key,attrs:{label:e.display_name,value:e.key}})}))],1)],1),e._v(" "),i("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[i("el-button",{on:{click:function(t){e.dialogFormVisible=!1}}},[e._v("取消")]),e._v(" "),i("el-button",{attrs:{type:"primary"},on:{click:e.updateData}},[e._v("确认")])],1)],1):e._e()],1)},[],!1,null,null,null);y.options.__file="message.vue";t.default=y.exports}}]);