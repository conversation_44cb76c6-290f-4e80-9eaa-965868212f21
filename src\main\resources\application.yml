server:
  port: 8080
spring:
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: ***********************************************************************************
    username: gxyan
    password:
mybatis:
  mapper-locations: classpath:mappers/*.xml
  type-aliases-package: com.gxyan.pojo
pagehelper:
    helperDialect: mysql
    reasonable: true
    supportMethodsArguments: true
    params: count=countSql
jwt:
    expiration: 3600
    header: Authorization