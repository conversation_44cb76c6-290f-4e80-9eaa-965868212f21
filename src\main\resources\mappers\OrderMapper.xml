<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gxyan.dao.OrderMapper">
  <resultMap id="BaseResultMap" type="com.gxyan.pojo.Order">
    <constructor>
      <idArg column="id" javaType="java.lang.Long" jdbcType="BIGINT" />
      <arg column="customer_id" javaType="java.lang.Long" jdbcType="BIGINT" />
      <arg column="employee_id" javaType="java.lang.Integer" jdbcType="INTEGER" />
      <arg column="total_price" javaType="java.math.BigDecimal" jdbcType="DECIMAL" />
      <arg column="status" javaType="java.lang.String" jdbcType="VARCHAR" />
      <arg column="create_time" javaType="java.util.Date" jdbcType="TIMESTAMP" />
      <arg column="pay_time" javaType="java.util.Date" jdbcType="TIMESTAMP" />
      <arg column="update_time" javaType="java.util.Date" jdbcType="TIMESTAMP" />
    </constructor>
  </resultMap>
  <sql id="Base_Column_List">
    id, customer_id, employee_id, total_price, status, create_time, pay_time, update_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from `order`
    where id = #{id,jdbcType=BIGINT}
  </select>
  <select id="selectSale" resultType="com.gxyan.vo.OrderList" parameterType="com.gxyan.vo.OrderQuery">
    select `order`.id orderId, c.id customerId, c.name customerName, c.phone customerPhone,
      c.id_card customerIdCard, e.name employeeName,
      total_price totalPrice, `order`.status status,
      `order`.create_time createTime, pay_time payTime, update_time updateTime
    from `order`
      inner join employee e on `order`.employee_id = e.id
      inner join customer c on `order`.customer_id = c.id
    <where>
      <if test="orderId != null and orderId != ''">
        and `order`.id = #{orderId}
      </if>
      <if test="customerName != null and customerName != ''">
        and c.name = #{customerName}
      </if>
      <if test="employeeName != null and employeeName != ''">
        and e.name = #{employeeName}
      </if>
      <if test="status != null and status != ''">
        and `order`.status = #{status}
      </if>
    </where>
    <if test="orderBy != null and orderBy != ''">
      order by ${orderBy}
    </if>
  </select>
  <select id="selectChartByDate" resultType="com.gxyan.vo.EmpChart" parameterType="string">
    select e.`name` name,SUM(total_price) value from `order`
      INNER JOIN employee e on `order`.employee_id = e.id
    where date_format(pay_time,'%Y-%m')=#{date}
    GROUP BY employee_id
  </select>
  <select id="selectYesterdayChart" resultType="com.gxyan.vo.EmpChart">
    select e.`name`,SUM(total_price) value from `order`
    INNER JOIN employee e on `order`.employee_id = e.id
    <![CDATA[
    where TO_DAYS( NOW( ) ) - TO_DAYS(pay_time) < 2 and TO_DAYS( NOW( ) ) - TO_DAYS(pay_time) >= 1
    ]]>
    GROUP BY employee_id
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from `order`
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.gxyan.pojo.Order">
    insert into `order` (id, customer_id, employee_id,
      total_price, status, create_time, update_time)
    values (#{id,jdbcType=BIGINT}, #{customerId,jdbcType=BIGINT}, #{employeeId,jdbcType=INTEGER},
      #{totalPrice,jdbcType=DECIMAL}, #{status,jdbcType=VARCHAR}, now(), now())
  </insert>
  <insert id="insertSelective" parameterType="com.gxyan.pojo.Order">
    insert into `order`
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="customerId != null">
        customer_id,
      </if>
      <if test="employeeId != null">
        employee_id,
      </if>
      <if test="totalPrice != null">
        total_price,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="payTime != null">
        pay_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="customerId != null">
        #{customerId,jdbcType=BIGINT},
      </if>
      <if test="employeeId != null">
        #{employeeId,jdbcType=INTEGER},
      </if>
      <if test="totalPrice != null">
        #{totalPrice,jdbcType=DECIMAL},
      </if>
      <if test="status != null">
        #{status,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="payTime != null">
        #{payTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.gxyan.pojo.Order">
    update `order`
    <set>
      <if test="customerId != null">
        customer_id = #{customerId,jdbcType=BIGINT},
      </if>
      <if test="employeeId != null">
        employee_id = #{employeeId,jdbcType=INTEGER},
      </if>
      <if test="totalPrice != null">
        total_price = #{totalPrice,jdbcType=DECIMAL},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = now(),
      </if>
      <if test="payTime != null">
        pay_time = #{payTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = now(),
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.gxyan.pojo.Order">
    update `order`
    set customer_id = #{customerId,jdbcType=BIGINT},
      employee_id = #{employeeId,jdbcType=INTEGER},
      total_price = #{totalPrice,jdbcType=DECIMAL},
      status = #{status,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      pay_time = #{payTime,jdbcType=TIMESTAMP},
      update_time = now()
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updatePayTimeByPrimaryKey">
    update `order`
    set pay_time = now(),
      update_time = now()
    where id = #{orderId} and status = 1
  </update>
  <update id="updateStatusByPrimaryKey">
    update `order`
    set status = #{status},
      update_time = now()
    where id = #{orderId} and status != 1
  </update>
  <update id="addTotalPriceByPrimaryKey">
    update `order`
    <![CDATA[
    set total_price = total_price + #{totalPrice},
      update_time = now()
    ]]>
    where id = #{orderId}
  </update>
</mapper>