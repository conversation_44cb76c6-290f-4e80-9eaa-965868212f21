(window.webpackJsonp=window.webpackJsonp||[]).push([["chunk-30e8"],{"1Bvf":function(e,t,n){},"9rC1":function(e,t,n){"use strict";n.d(t,"a",function(){return a}),n.d(t,"b",function(){return r}),n.d(t,"c",function(){return o});var i=n("t3Un");function a(e){return Object(i.a)({url:"/customer/addCustomer",method:"get",params:e})}function r(e){return Object(i.a)({url:"/customer/getList",method:"get",params:e})}function o(e){return Object(i.a)({url:"/customer/update",method:"post",params:e})}},Bfuw:function(e,t,n){var i=n("XdqN"),a=Math.floor;e.exports=function(e){return!i(e)&&isFinite(e)&&a(e)===e}},"K/D6":function(e,t,n){"use strict";var i=n("1Bvf");n.n(i).a},Mz3J:function(e,t,n){"use strict";Math.easeInOutQuad=function(e,t,n,i){return(e/=i/2)<1?n/2*e*e+t:-n/2*(--e*(e-2)-1)+t};var i=window.requestAnimationFrame||window.webkitRequestAnimationFrame||window.mozRequestAnimationFrame||function(e){window.setTimeout(e,1e3/60)};function a(e,t,n){var a=document.documentElement.scrollTop||document.body.parentNode.scrollTop||document.body.scrollTop,r=e-a,o=0;t=void 0===t?500:t;!function e(){o+=20,function(e){document.documentElement.scrollTop=e,document.body.parentNode.scrollTop=e,document.body.scrollTop=e}(Math.easeInOutQuad(o,a,r,t)),o<t?i(e):n&&"function"==typeof n&&n()}()}var r={name:"Pagination",props:{total:{required:!0,type:Number},page:{type:Number,default:1},limit:{type:Number,default:5},pageSizes:{type:Array,default:function(){return[5,10,20,50]}},layout:{type:String,default:"total, sizes, prev, pager, next, jumper"},background:{type:Boolean,default:!0},autoScroll:{type:Boolean,default:!0},hidden:{type:Boolean,default:!1}},computed:{currentPage:{get:function(){return this.page},set:function(e){this.$emit("update:page",e)}},pageSize:{get:function(){return this.limit},set:function(e){this.$emit("update:limit",e)}}},methods:{handleSizeChange:function(e){this.$emit("pagination",{page:this.currentPage,limit:e}),this.autoScroll&&a(0,800)},handleCurrentChange:function(e){this.$emit("pagination",{page:e,limit:this.pageSize}),this.autoScroll&&a(0,800)}}},o=(n("K/D6"),n("ZrdR")),l=Object(o.a)(r,function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticClass:"pagination-container",class:{hidden:e.hidden}},[n("el-pagination",e._b({attrs:{background:e.background,"current-page":e.currentPage,"page-size":e.pageSize,layout:e.layout,"page-sizes":e.pageSizes,total:e.total},on:{"update:currentPage":function(t){e.currentPage=t},"update:pageSize":function(t){e.pageSize=t},"size-change":e.handleSizeChange,"current-change":e.handleCurrentChange}},"el-pagination",e.$attrs,!1))],1)},[],!1,null,"6fb7ae22",null);l.options.__file="index.vue";t.a=l.exports},Yfch:function(e,t,n){"use strict";n.d(t,"a",function(){return r}),n.d(t,"c",function(){return o}),n.d(t,"b",function(){return l}),n.d(t,"d",function(){return s});var i=n("e9eg"),a=n.n(i);function r(e){return/^[1-9]\d{5}(18|19|([23]\d))\d{2}((0[1-9])|(10|11|12))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/.test(e)}var o=function(e,t,n){if(""===t||void 0===t)n(new Error("请输入身份证号"));else{/^[1-9]\d{5}(18|19|([23]\d))\d{2}((0[1-9])|(10|11|12))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/.test(t)||n(new Error("请输入正确的身份证号")),n()}},l=function(e,t,n){if(!t)return n(new Error("年龄不能为空"));setTimeout(function(){a()(t)?t<18?n(new Error("必须年满18岁")):n():n(new Error("请输入数字值"))},1e3)},s=function(e,t,n){if(!t)return n(new Error("请输入员工薪资"));setTimeout(function(){a()(t)?t<1800?n(new Error("薪资必须大于1800元")):n():n(new Error("请输入数字值"))},1e3)}},ZySA:function(e,t,n){"use strict";var i=n("6ZY3"),a=n.n(i),r=(n("jUE0"),{bind:function(e,t){e.addEventListener("click",function(n){var i=a()({},t.value),r=a()({ele:e,type:"hit",color:"rgba(0, 0, 0, 0.15)"},i),o=r.ele;if(o){o.style.position="relative",o.style.overflow="hidden";var l=o.getBoundingClientRect(),s=o.querySelector(".waves-ripple");switch(s?s.className="waves-ripple":((s=document.createElement("span")).className="waves-ripple",s.style.height=s.style.width=Math.max(l.width,l.height)+"px",o.appendChild(s)),r.type){case"center":s.style.top=l.height/2-s.offsetHeight/2+"px",s.style.left=l.width/2-s.offsetWidth/2+"px";break;default:s.style.top=(n.pageY-l.top-s.offsetHeight/2-document.documentElement.scrollTop||document.body.scrollTop)+"px",s.style.left=(n.pageX-l.left-s.offsetWidth/2-document.documentElement.scrollLeft||document.body.scrollLeft)+"px"}return s.style.backgroundColor=r.color,s.className="waves-ripple z-active",!1}},!1)}}),o=function(e){e.directive("waves",r)};window.Vue&&(window.waves=r,Vue.use(o)),r.install=o;t.a=r},e9eg:function(e,t,n){e.exports={default:n("t3Qm"),__esModule:!0}},f3ia:function(e,t,n){"use strict";n.r(t);var i=n("rerW"),a=n.n(i),r=n("6ZY3"),o=n.n(r),l=n("9rC1"),s=n("ZySA"),u=n("7Qib"),d=n("Mz3J"),c=n("Yfch"),p={name:"ComplexTable",components:{Pagination:d.a},directives:{waves:s.a},data:function(){return{tableKey:0,list:null,total:0,listLoading:!0,listQuery:{page:1,limit:5,id:void 0,name:null,phone:void 0,idCard:void 0,orderBy:null},temp:{name:"",phone:"",idCard:""},dialogFormVisible:!1,rules:{name:[{required:!0,message:"请输入客户姓名",trigger:"blur"}],phone:[{required:!0,message:"请输入客户联系电话",trigger:"blur"}],idCard:[{required:!0,validator:c.c,trigger:"blur"}]},downloadLoading:!1}},created:function(){this.getList()},methods:{getList:function(){var e=this;this.listLoading=!0,Object(l.b)(this.listQuery).then(function(t){2e4===t.data.code?(e.list=t.data.data.items,e.total=t.data.data.total):e.$message({message:t.data.message,type:"error"}),e.listLoading=!1})},handleFilter:function(){this.listQuery.page=1,this.getList()},handleModifyStatus:function(e,t){this.$message({message:"操作成功",type:"success"}),e.status=t},sortChange:function(e){var t=e.prop,n=e.order;this.listQuery.orderBy="ascending"===n?t+" asc":t+" desc",this.handleFilter()},handleUpdate:function(e){var t=this;this.temp=o()({},e),this.dialogFormVisible=!0,this.$nextTick(function(){t.$refs.dataForm.clearValidate()})},updateData:function(){var e=this;this.$refs.dataForm.validate(function(t){if(t){var n=o()({},e.temp);n.createTime=null,Object(l.c)(n).then(function(t){if(2e4===t.data.code){var n=!0,i=!1,r=void 0;try{for(var o,l=a()(e.list);!(n=(o=l.next()).done);n=!0){var s=o.value;if(s.id===e.temp.id){var u=e.list.indexOf(s);e.list.splice(u,1,e.temp);break}}}catch(e){i=!0,r=e}finally{try{!n&&l.return&&l.return()}finally{if(i)throw r}}e.dialogFormVisible=!1,e.$notify({title:"成功",message:"更新成功",type:"success",duration:2e3})}else e.$notify({title:"错误",message:t.data.message,type:"error",duration:2e3})})}})},handleDownload:function(){var e=this;this.downloadLoading=!0,Promise.all([n.e("chunk-3806"),n.e("chunk-5556")]).then(n.bind(null,"S/jZ")).then(function(t){var n=e.formatJson(["id","name","phone","idCard","createTime"],e.list);t.export_json_to_excel({header:["客户编号","姓名","手机号","身份证号","注册时间"],data:n,filename:"客户信息"}),e.downloadLoading=!1})},formatJson:function(e,t){return t.map(function(t){return e.map(function(e){return"createTime"===e?Object(u.d)(t[e]):t[e]})})}}},m=n("ZrdR"),f=Object(m.a)(p,function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticClass:"app-container"},[n("div",{staticClass:"filter-container"},[n("el-input",{staticClass:"filter-item",staticStyle:{width:"160px"},attrs:{placeholder:"客户编号",clearable:""},nativeOn:{keyup:function(t){return"button"in t||!e._k(t.keyCode,"enter",13,t.key,"Enter")?e.handleFilter(t):null}},model:{value:e.listQuery.id,callback:function(t){e.$set(e.listQuery,"id",t)},expression:"listQuery.id"}}),e._v(" "),n("el-input",{staticClass:"filter-item",staticStyle:{width:"150px"},attrs:{placeholder:"客户姓名",clearable:""},nativeOn:{keyup:function(t){return"button"in t||!e._k(t.keyCode,"enter",13,t.key,"Enter")?e.handleFilter(t):null}},model:{value:e.listQuery.name,callback:function(t){e.$set(e.listQuery,"name",t)},expression:"listQuery.name"}}),e._v(" "),n("el-input",{staticClass:"filter-item",staticStyle:{width:"150px"},attrs:{placeholder:"手机号",clearable:""},nativeOn:{keyup:function(t){return"button"in t||!e._k(t.keyCode,"enter",13,t.key,"Enter")?e.handleFilter(t):null}},model:{value:e.listQuery.phone,callback:function(t){e.$set(e.listQuery,"phone",t)},expression:"listQuery.phone"}}),e._v(" "),n("el-input",{staticClass:"filter-item",staticStyle:{width:"190px"},attrs:{placeholder:"身份证号",clearable:""},nativeOn:{keyup:function(t){return"button"in t||!e._k(t.keyCode,"enter",13,t.key,"Enter")?e.handleFilter(t):null}},model:{value:e.listQuery.idCard,callback:function(t){e.$set(e.listQuery,"idCard",t)},expression:"listQuery.idCard"}}),e._v(" "),n("el-button",{directives:[{name:"waves",rawName:"v-waves"}],staticClass:"filter-item",attrs:{type:"primary",icon:"el-icon-search"},on:{click:e.handleFilter}},[e._v("搜索")]),e._v(" "),n("el-button",{directives:[{name:"waves",rawName:"v-waves"}],staticClass:"filter-item",attrs:{loading:e.downloadLoading,type:"primary",icon:"el-icon-download"},on:{click:e.handleDownload}},[e._v("导出")])],1),e._v(" "),n("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.listLoading,expression:"listLoading"}],key:e.tableKey,staticStyle:{width:"100%"},attrs:{data:e.list,border:"",fit:"","highlight-current-row":""},on:{"sort-change":e.sortChange}},[n("el-table-column",{attrs:{label:"客户编号",prop:"id",sortable:"custom",align:"center","min-width":"105px"},scopedSlots:e._u([{key:"default",fn:function(t){return[n("span",[e._v(e._s(t.row.id))])]}}])}),e._v(" "),n("el-table-column",{attrs:{label:"姓名",prop:"name",width:"80px",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[n("span",[e._v(e._s(t.row.name))])]}}])}),e._v(" "),n("el-table-column",{attrs:{label:"联系电话",prop:"phone",width:"115px",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[n("span",[e._v(e._s(t.row.phone))])]}}])}),e._v(" "),n("el-table-column",{attrs:{label:"身份证号",prop:"idCard",align:"center","min-width":"150px"},scopedSlots:e._u([{key:"default",fn:function(t){return[n("span",[e._v(e._s(t.row.idCard))])]}}])}),e._v(" "),n("el-table-column",{attrs:{label:"注册时间",prop:"createTime",sortable:"custom","min-width":"135px",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[n("span",[e._v(e._s(e._f("parseTime")(t.row.createTime,"{y}-{m}-{d}")))])]}}])}),e._v(" "),n("el-table-column",{attrs:{label:"操作",align:"center","min-width":"100px","class-name":"small-padding fixed-width"},scopedSlots:e._u([{key:"default",fn:function(t){return[n("el-button",{attrs:{type:"primary",size:"mini"},on:{click:function(n){e.handleUpdate(t.row)}}},[e._v("编辑")])]}}])})],1),e._v(" "),n("pagination",{directives:[{name:"show",rawName:"v-show",value:e.total>0,expression:"total>0"}],attrs:{total:e.total,page:e.listQuery.page,limit:e.listQuery.limit},on:{"update:page":function(t){e.$set(e.listQuery,"page",t)},"update:limit":function(t){e.$set(e.listQuery,"limit",t)},pagination:e.getList}}),e._v(" "),n("el-dialog",{attrs:{visible:e.dialogFormVisible,title:"修改客户信息"},on:{"update:visible":function(t){e.dialogFormVisible=t}}},[n("el-form",{ref:"dataForm",staticStyle:{width:"400px","margin-left":"50px"},attrs:{rules:e.rules,model:e.temp,"label-position":"left","label-width":"100px"}},[n("el-form-item",{attrs:{label:"姓名",prop:"name"}},[n("el-input",{model:{value:e.temp.name,callback:function(t){e.$set(e.temp,"name",t)},expression:"temp.name"}})],1),e._v(" "),n("el-form-item",{attrs:{label:"联系电话",prop:"phone"}},[n("el-input",{model:{value:e.temp.phone,callback:function(t){e.$set(e.temp,"phone",t)},expression:"temp.phone"}})],1),e._v(" "),n("el-form-item",{attrs:{label:"身份证号",prop:"idCard"}},[n("el-input",{model:{value:e.temp.idCard,callback:function(t){e.$set(e.temp,"idCard",t)},expression:"temp.idCard"}})],1),e._v(" "),n("el-form-item",[n("el-button",{on:{click:function(t){e.dialogFormVisible=!1}}},[e._v("取消")]),e._v(" "),n("el-button",{attrs:{type:"primary"},on:{click:function(t){e.updateData()}}},[e._v("确认")])],1)],1)],1)],1)},[],!1,null,null,null);f.options.__file="message.vue";t.default=f.exports},jUE0:function(e,t,n){},kmh5:function(e,t,n){var i=n("NCe+");i(i.S,"Number",{isInteger:n("Bfuw")})},t3Qm:function(e,t,n){n("kmh5"),e.exports=n("f4ri").Number.isInteger}}]);