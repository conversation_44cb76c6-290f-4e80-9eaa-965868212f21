(window.webpackJsonp=window.webpackJsonp||[]).push([["chunk-719c"],{"3KUV":function(e,r,t){"use strict";t.d(r,"c",function(){return i}),t.d(r,"a",function(){return s}),t.d(r,"d",function(){return n}),t.d(r,"b",function(){return l});var a=t("t3Un");function i(){return Object(a.a)({url:"/init/seriesOpt",method:"get"})}function s(){return Object(a.a)({url:"/init/brandOpt",method:"get"})}function n(e){return Object(a.a)({url:"/init/storeOpt",method:"get",params:{seriesId:e}})}function l(e){return Object(a.a)({url:"/init/getCustomer",method:"get",params:{idCard:e}})}},"c/X4":function(e,r,t){"use strict";t.d(r,"a",function(){return i}),t.d(r,"d",function(){return s}),t.d(r,"b",function(){return n}),t.d(r,"e",function(){return l}),t.d(r,"c",function(){return d}),t.d(r,"f",function(){return o}),t.d(r,"g",function(){return u});var a=t("t3Un");function i(e){return Object(a.a)({url:"/store/addBrand",method:"get",params:{brand:e}})}function s(e){return Object(a.a)({url:"/store/delBrand",method:"get",params:{brandId:e}})}function n(e,r){return Object(a.a)({url:"/store/addSeries",method:"get",params:{brandId:e,seriesName:r}})}function l(e){return Object(a.a)({url:"/store/delSeries",method:"get",params:{seriesId:e}})}function d(e){return Object(a.a)({url:"/store/addStore",method:"post",params:e})}function o(e){return Object(a.a)({url:"/store/getList",method:"get",params:e})}function u(e){return console.log(e),Object(a.a)({url:"/store/update",method:"post",params:e})}},caRP:function(e,r,t){"use strict";var a=t("tYUR");t.n(a).a},skIc:function(e,r,t){"use strict";t.r(r);var a=t("6ZY3"),i=t.n(a),s=t("c/X4"),n=t("3KUV"),l={data:function(){return{ruleForm:{seriesId:[],type:"",color:"",price:void 0,salePrice:void 0,repertory:void 0},addBrand:{brand:""},brandOptions:[],seriesOptions:[],delBrand:{brandId:void 0},addSeries:{brandId:void 0,series:""},delSeries:{series:[]},rules:{seriesId:[{required:!0,message:"请输入品牌 & 车系"}],type:[{required:!0,message:"请输入车辆型号",trigger:"blur"}],color:[{required:!0,message:"请输入车辆颜色",trigger:"blur"}],price:[{required:!0,message:"请输入车辆进价",trigger:"blur"},{type:"number",message:"进价必须为数字值"}],salePrice:[{required:!0,message:"请输入车辆售价",trigger:"blur"},{type:"number",message:"售价必须为数字值"}],repertory:[{required:!0,message:"请输入车辆库存"}]},options:[]}},created:function(){this.getSeriesOpt(),this.getBrandOpt()},methods:{getSeriesOpt:function(){var e=this;Object(n.c)().then(function(r){e.options=r.data.data})},getBrandOpt:function(){var e=this;Object(n.a)().then(function(r){e.brandOptions=r.data.data})},submitStore:function(){var e=this;this.$refs.ruleForm.validate(function(r){if(!r)return console.log("error submit!!"),!1;var t=i()({},e.ruleForm);t.seriesId=e.ruleForm.seriesId[1],Object(s.c)(t).then(function(r){e.message(r)})})},submitBrand:function(){var e=this;this.$refs.addBrand.validate(function(r){if(!r)return!1;Object(s.a)(e.addBrand.brand).then(function(r){e.message(r),e.getBrandOpt(),e.getSeriesOpt()})})},submitDelBrand:function(){var e=this;this.$refs.delBrand.validate(function(r){if(!r)return!1;Object(s.d)(e.delBrand.brandId).then(function(r){e.message(r),e.getBrandOpt(),e.getSeriesOpt()})})},submitSeries:function(){var e=this;this.$refs.addSeries.validate(function(r){if(!r)return!1;Object(s.b)(e.addSeries.brandId,e.addSeries.series).then(function(r){e.message(r),e.getSeriesOpt()})})},submitDelSeries:function(){var e=this;if(null==this.delSeries.series[1])return console.log("error del"),!1;Object(s.e)(this.delSeries.series[1]).then(function(r){e.message(r),e.getSeriesOpt()})},resetForm:function(e){this.$refs[e].resetFields()},message:function(e){2e4===e.data.code?this.$notify({title:"成功",type:"success",duration:2e3}):this.$notify({title:"错误",message:e.data.message,type:"error",duration:2e3})}}},d=(t("caRP"),t("ZrdR")),o=Object(d.a)(l,function(){var e=this,r=e.$createElement,t=e._self._c||r;return t("div",{staticClass:"app-container"},[t("el-row",{attrs:{gutter:24}},[t("el-col",{attrs:{xs:24,sm:24,lg:12}},[t("div",{staticClass:"chart-wrapper",staticStyle:{"padding-bottom":"5px"}},[t("el-header",[t("h3",[e._v("添加库存")]),e._v(" "),t("hr")]),e._v(" "),t("el-form",{ref:"ruleForm",staticStyle:{width:"400px","margin-left":"40px"},attrs:{model:e.ruleForm,rules:e.rules,"label-width":"100px"}},[t("el-form-item",{attrs:{label:"品牌 & 车系",prop:"seriesId"}},[t("el-cascader",{staticStyle:{width:"220px"},attrs:{options:e.options,placeholder:"品牌/车系"},model:{value:e.ruleForm.seriesId,callback:function(r){e.$set(e.ruleForm,"seriesId",r)},expression:"ruleForm.seriesId"}})],1),e._v(" "),t("el-form-item",{attrs:{label:"型号",prop:"type"}},[t("el-input",{model:{value:e.ruleForm.type,callback:function(r){e.$set(e.ruleForm,"type",r)},expression:"ruleForm.type"}})],1),e._v(" "),t("el-form-item",{attrs:{label:"颜色",prop:"color"}},[t("el-input",{model:{value:e.ruleForm.color,callback:function(r){e.$set(e.ruleForm,"color",r)},expression:"ruleForm.color"}})],1),e._v(" "),t("el-form-item",{attrs:{label:"进价",prop:"price"}},[t("el-input",{model:{value:e.ruleForm.price,callback:function(r){e.$set(e.ruleForm,"price",e._n(r))},expression:"ruleForm.price"}})],1),e._v(" "),t("el-form-item",{attrs:{label:"售价",prop:"salePrice"}},[t("el-input",{model:{value:e.ruleForm.salePrice,callback:function(r){e.$set(e.ruleForm,"salePrice",e._n(r))},expression:"ruleForm.salePrice"}})],1),e._v(" "),t("el-form-item",{attrs:{label:"数量",prop:"repertory"}},[t("el-input-number",{attrs:{min:0},model:{value:e.ruleForm.repertory,callback:function(r){e.$set(e.ruleForm,"repertory",r)},expression:"ruleForm.repertory"}})],1),e._v(" "),t("el-form-item",[t("el-button",{attrs:{type:"primary"},on:{click:e.submitStore}},[e._v("添加")]),e._v(" "),t("el-button",{on:{click:function(r){e.resetForm("ruleForm")}}},[e._v("重置")])],1)],1)],1)]),e._v(" "),t("el-col",{attrs:{xs:24,sm:24,lg:12}},[t("div",{staticClass:"chart-wrapper"},[t("el-header",[t("h3",[e._v("添加品牌")]),e._v(" "),t("hr")]),e._v(" "),t("el-form",{ref:"addBrand",staticStyle:{width:"420px","margin-left":"50px"},attrs:{model:e.addBrand,inline:!0,"label-width":"90px"}},[t("el-form-item",{attrs:{label:"品牌名称",prop:"brand",required:""}},[t("el-input",{model:{value:e.addBrand.brand,callback:function(r){e.$set(e.addBrand,"brand",r)},expression:"addBrand.brand"}})],1),e._v(" "),t("el-form-item",[t("el-button",{attrs:{type:"primary"},on:{click:e.submitBrand}},[e._v("添加")])],1)],1)],1),e._v(" "),t("div",{staticClass:"chart-wrapper"},[t("el-header",[t("h3",[e._v("删除品牌")]),e._v(" "),t("hr")]),e._v(" "),t("el-form",{ref:"delBrand",staticStyle:{width:"420px","margin-left":"50px"},attrs:{model:e.delBrand,inline:!0,"label-width":"90px"}},[t("el-form-item",{attrs:{label:"品牌名称",required:""}},[t("el-select",{staticClass:"filter-item",attrs:{placeholder:"请选择"},model:{value:e.delBrand.brandId,callback:function(r){e.$set(e.delBrand,"brandId",r)},expression:"delBrand.brandId"}},e._l(e.brandOptions,function(e){return t("el-option",{key:e.brandId,attrs:{label:e.brandName,value:e.brandId}})}))],1),e._v(" "),t("el-form-item",[t("el-button",{attrs:{type:"danger"},on:{click:e.submitDelBrand}},[e._v("删除")])],1)],1)],1),e._v(" "),t("div",{staticClass:"chart-wrapper"},[t("el-header",[t("h3",[e._v("添加车系")]),e._v(" "),t("hr")]),e._v(" "),t("el-form",{ref:"addSeries",staticStyle:{width:"420px","margin-left":"50px"},attrs:{model:e.addSeries,inline:!0,"label-width":"90px"}},[t("el-form-item",{attrs:{label:"车辆品牌",required:""}},[t("el-select",{staticClass:"filter-item",attrs:{placeholder:"请选择"},model:{value:e.addSeries.brandId,callback:function(r){e.$set(e.addSeries,"brandId",r)},expression:"addSeries.brandId"}},e._l(e.brandOptions,function(e){return t("el-option",{key:e.brandId,attrs:{label:e.brandName,value:e.brandId}})}))],1),e._v(" "),t("br"),e._v(" "),t("el-form-item",{attrs:{label:"车系名称",required:""}},[t("el-input",{staticStyle:{width:"220px"},model:{value:e.addSeries.series,callback:function(r){e.$set(e.addSeries,"series",r)},expression:"addSeries.series"}})],1),e._v(" "),t("el-form-item",[t("el-button",{attrs:{type:"primary"},on:{click:e.submitSeries}},[e._v("添加")])],1)],1)],1),e._v(" "),t("div",{staticClass:"chart-wrapper"},[t("el-header",[t("h3",[e._v("删除车系")]),e._v(" "),t("hr")]),e._v(" "),t("el-form",{ref:"delSeries",staticStyle:{width:"420px","margin-left":"50px"},attrs:{model:e.delSeries,inline:!0,"label-width":"100px"}},[t("el-form-item",{attrs:{label:"品牌 & 车系",required:""}},[t("el-cascader",{staticStyle:{width:"220px"},attrs:{options:e.options,placeholder:"品牌/车系"},model:{value:e.delSeries.series,callback:function(r){e.$set(e.delSeries,"series",r)},expression:"delSeries.series"}})],1),e._v(" "),t("el-form-item",[t("el-button",{attrs:{type:"danger"},on:{click:e.submitDelSeries}},[e._v("删除")])],1)],1)],1)])],1)],1)},[],!1,null,"32a4d33e",null);o.options.__file="addStore.vue";r.default=o.exports},tYUR:function(e,r,t){}}]);