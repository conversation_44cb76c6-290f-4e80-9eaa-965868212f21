/*
 Navicat Premium Data Transfer

 Source Server         : 阿里云ubuntu
 Source Server Type    : MySQL
 Source Server Version : 50725
 Source Host           : localhost:3306
 Source Schema         : carsale

 Target Server Type    : MySQL
 Target Server Version : 50725
 File Encoding         : 65001

 Date: 14/05/2019 22:28:42
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for base_month
-- ----------------------------
DROP TABLE IF EXISTS `base_month`;
CREATE TABLE `base_month`  (
  `date` varchar(10) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of base_month
-- ----------------------------
INSERT INTO `base_month` VALUES ('2018-01');
INSERT INTO `base_month` VALUES ('2018-02');
INSERT INTO `base_month` VALUES ('2018-03');
INSERT INTO `base_month` VALUES ('2018-04');
INSERT INTO `base_month` VALUES ('2018-05');
INSERT INTO `base_month` VALUES ('2018-06');
INSERT INTO `base_month` VALUES ('2018-07');
INSERT INTO `base_month` VALUES ('2018-08');
INSERT INTO `base_month` VALUES ('2018-09');
INSERT INTO `base_month` VALUES ('2018-10');
INSERT INTO `base_month` VALUES ('2018-11');
INSERT INTO `base_month` VALUES ('2018-12');
INSERT INTO `base_month` VALUES ('2019-01');
INSERT INTO `base_month` VALUES ('2019-02');
INSERT INTO `base_month` VALUES ('2019-03');
INSERT INTO `base_month` VALUES ('2019-04');
INSERT INTO `base_month` VALUES ('2019-05');
INSERT INTO `base_month` VALUES ('2019-06');
INSERT INTO `base_month` VALUES ('2019-07');
INSERT INTO `base_month` VALUES ('2019-08');
INSERT INTO `base_month` VALUES ('2019-09');
INSERT INTO `base_month` VALUES ('2019-10');
INSERT INTO `base_month` VALUES ('2019-11');
INSERT INTO `base_month` VALUES ('2019-12');
INSERT INTO `base_month` VALUES ('2020-01');
INSERT INTO `base_month` VALUES ('2020-02');
INSERT INTO `base_month` VALUES ('2020-03');
INSERT INTO `base_month` VALUES ('2020-04');
INSERT INTO `base_month` VALUES ('2020-05');
INSERT INTO `base_month` VALUES ('2020-06');
INSERT INTO `base_month` VALUES ('2020-07');
INSERT INTO `base_month` VALUES ('2020-08');
INSERT INTO `base_month` VALUES ('2020-09');
INSERT INTO `base_month` VALUES ('2020-10');
INSERT INTO `base_month` VALUES ('2020-11');
INSERT INTO `base_month` VALUES ('2020-12');
INSERT INTO `base_month` VALUES ('2021-01');
INSERT INTO `base_month` VALUES ('2021-02');
INSERT INTO `base_month` VALUES ('2021-03');
INSERT INTO `base_month` VALUES ('2021-04');
INSERT INTO `base_month` VALUES ('2021-05');
INSERT INTO `base_month` VALUES ('2021-06');
INSERT INTO `base_month` VALUES ('2021-07');
INSERT INTO `base_month` VALUES ('2021-08');
INSERT INTO `base_month` VALUES ('2021-09');
INSERT INTO `base_month` VALUES ('2021-10');
INSERT INTO `base_month` VALUES ('2021-11');
INSERT INTO `base_month` VALUES ('2021-12');
INSERT INTO `base_month` VALUES ('2022-01');
INSERT INTO `base_month` VALUES ('2022-02');
INSERT INTO `base_month` VALUES ('2022-03');
INSERT INTO `base_month` VALUES ('2022-04');
INSERT INTO `base_month` VALUES ('2022-05');
INSERT INTO `base_month` VALUES ('2022-06');
INSERT INTO `base_month` VALUES ('2022-07');
INSERT INTO `base_month` VALUES ('2022-08');
INSERT INTO `base_month` VALUES ('2022-09');
INSERT INTO `base_month` VALUES ('2022-10');
INSERT INTO `base_month` VALUES ('2022-11');
INSERT INTO `base_month` VALUES ('2022-12');
INSERT INTO `base_month` VALUES ('2023-01');
INSERT INTO `base_month` VALUES ('2023-02');
INSERT INTO `base_month` VALUES ('2023-03');
INSERT INTO `base_month` VALUES ('2023-04');
INSERT INTO `base_month` VALUES ('2023-05');
INSERT INTO `base_month` VALUES ('2023-06');
INSERT INTO `base_month` VALUES ('2023-07');
INSERT INTO `base_month` VALUES ('2023-08');
INSERT INTO `base_month` VALUES ('2023-09');
INSERT INTO `base_month` VALUES ('2023-10');
INSERT INTO `base_month` VALUES ('2023-11');
INSERT INTO `base_month` VALUES ('2023-12');
INSERT INTO `base_month` VALUES ('2024-01');
INSERT INTO `base_month` VALUES ('2024-02');
INSERT INTO `base_month` VALUES ('2024-03');
INSERT INTO `base_month` VALUES ('2024-04');
INSERT INTO `base_month` VALUES ('2024-05');
INSERT INTO `base_month` VALUES ('2024-06');
INSERT INTO `base_month` VALUES ('2024-07');
INSERT INTO `base_month` VALUES ('2024-08');
INSERT INTO `base_month` VALUES ('2024-09');
INSERT INTO `base_month` VALUES ('2024-10');
INSERT INTO `base_month` VALUES ('2024-11');
INSERT INTO `base_month` VALUES ('2024-12');
INSERT INTO `base_month` VALUES ('2025-01');
INSERT INTO `base_month` VALUES ('2025-02');
INSERT INTO `base_month` VALUES ('2025-03');
INSERT INTO `base_month` VALUES ('2025-04');
INSERT INTO `base_month` VALUES ('2025-05');
INSERT INTO `base_month` VALUES ('2025-06');
INSERT INTO `base_month` VALUES ('2025-07');
INSERT INTO `base_month` VALUES ('2025-08');
INSERT INTO `base_month` VALUES ('2025-09');
INSERT INTO `base_month` VALUES ('2025-10');
INSERT INTO `base_month` VALUES ('2025-11');
INSERT INTO `base_month` VALUES ('2025-12');
INSERT INTO `base_month` VALUES ('2026-01');
INSERT INTO `base_month` VALUES ('2026-02');
INSERT INTO `base_month` VALUES ('2026-03');
INSERT INTO `base_month` VALUES ('2026-04');
INSERT INTO `base_month` VALUES ('2026-05');
INSERT INTO `base_month` VALUES ('2026-06');
INSERT INTO `base_month` VALUES ('2026-07');
INSERT INTO `base_month` VALUES ('2026-08');
INSERT INTO `base_month` VALUES ('2026-09');
INSERT INTO `base_month` VALUES ('2026-10');
INSERT INTO `base_month` VALUES ('2026-11');
INSERT INTO `base_month` VALUES ('2026-12');
INSERT INTO `base_month` VALUES ('2027-01');
INSERT INTO `base_month` VALUES ('2027-02');
INSERT INTO `base_month` VALUES ('2027-03');
INSERT INTO `base_month` VALUES ('2027-04');
INSERT INTO `base_month` VALUES ('2027-05');
INSERT INTO `base_month` VALUES ('2027-06');
INSERT INTO `base_month` VALUES ('2027-07');
INSERT INTO `base_month` VALUES ('2027-08');
INSERT INTO `base_month` VALUES ('2027-09');
INSERT INTO `base_month` VALUES ('2027-10');
INSERT INTO `base_month` VALUES ('2027-11');
INSERT INTO `base_month` VALUES ('2027-12');
INSERT INTO `base_month` VALUES ('2028-01');
INSERT INTO `base_month` VALUES ('2028-02');
INSERT INTO `base_month` VALUES ('2028-03');
INSERT INTO `base_month` VALUES ('2028-04');
INSERT INTO `base_month` VALUES ('2028-05');
INSERT INTO `base_month` VALUES ('2028-06');
INSERT INTO `base_month` VALUES ('2028-07');
INSERT INTO `base_month` VALUES ('2028-08');
INSERT INTO `base_month` VALUES ('2028-09');
INSERT INTO `base_month` VALUES ('2028-10');
INSERT INTO `base_month` VALUES ('2028-11');
INSERT INTO `base_month` VALUES ('2028-12');
INSERT INTO `base_month` VALUES ('2029-01');
INSERT INTO `base_month` VALUES ('2029-02');
INSERT INTO `base_month` VALUES ('2029-03');
INSERT INTO `base_month` VALUES ('2029-04');
INSERT INTO `base_month` VALUES ('2029-05');
INSERT INTO `base_month` VALUES ('2029-06');
INSERT INTO `base_month` VALUES ('2029-07');
INSERT INTO `base_month` VALUES ('2029-08');
INSERT INTO `base_month` VALUES ('2029-09');
INSERT INTO `base_month` VALUES ('2029-10');
INSERT INTO `base_month` VALUES ('2029-11');
INSERT INTO `base_month` VALUES ('2029-12');
INSERT INTO `base_month` VALUES ('2030-01');
INSERT INTO `base_month` VALUES ('2030-02');
INSERT INTO `base_month` VALUES ('2030-03');
INSERT INTO `base_month` VALUES ('2030-04');
INSERT INTO `base_month` VALUES ('2030-05');
INSERT INTO `base_month` VALUES ('2030-06');
INSERT INTO `base_month` VALUES ('2030-07');
INSERT INTO `base_month` VALUES ('2030-08');
INSERT INTO `base_month` VALUES ('2030-09');
INSERT INTO `base_month` VALUES ('2030-10');
INSERT INTO `base_month` VALUES ('2030-11');
INSERT INTO `base_month` VALUES ('2030-12');

-- ----------------------------
-- Table structure for brand
-- ----------------------------
DROP TABLE IF EXISTS `brand`;
CREATE TABLE `brand`  (
  `brand_id` int(11) NOT NULL AUTO_INCREMENT COMMENT '品牌编号',
  `brand_name` varchar(16) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '品牌名称',
  `status` varchar(1) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '1' COMMENT '品牌状态（0：删除状态，1：正常状态）',
  PRIMARY KEY (`brand_id`) USING BTREE,
  UNIQUE INDEX `brand_name`(`brand_name`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 6 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of brand
-- ----------------------------
INSERT INTO `brand` VALUES (1, '宝马', '1');
INSERT INTO `brand` VALUES (2, '奔驰', '1');
INSERT INTO `brand` VALUES (3, '奥迪', '1');
INSERT INTO `brand` VALUES (4, '本田', '1');
INSERT INTO `brand` VALUES (5, '丰田', '1');

-- ----------------------------
-- Table structure for car
-- ----------------------------
DROP TABLE IF EXISTS `car`;
CREATE TABLE `car`  (
  `id` bigint(20) NOT NULL COMMENT '车辆编号',
  `series_id` int(11) NOT NULL COMMENT '车系编号',
  `type` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '车辆型号',
  `color` varchar(16) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '车辆颜色',
  `price` decimal(12, 2) NOT NULL COMMENT '车辆进价',
  `sale_price` decimal(12, 2) NOT NULL COMMENT '车辆售价',
  `repertory` int(11) NOT NULL COMMENT '车辆库存',
  `create_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '车辆入库时间',
  `status` varchar(1) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '车辆状态（0：停售，1：在售，2：停售）',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `series_id`(`series_id`) USING BTREE,
  CONSTRAINT `car_ibfk_1` FOREIGN KEY (`series_id`) REFERENCES `series` (`series_id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of car
-- ----------------------------
INSERT INTO `car` VALUES (19010400001, 2, '2018款 730Li 领先型 M运动套装', '矿石白', 790000.00, 830000.00, 0, '2018-12-04 17:20:13', '0');
INSERT INTO `car` VALUES (19010400002, 3, '2019款 525Li 豪华套装', '宝石青', 410000.00, 439000.00, 1, '2019-01-04 17:22:35', '1');
INSERT INTO `car` VALUES (19010400003, 3, '2019款 530Li 尊享型 豪华套装', '勃艮第红', 498000.00, 523000.00, 2, '2019-01-04 17:24:50', '1');
INSERT INTO `car` VALUES (19010400004, 4, '2019款 E 320 L 4MATIC', '曜岩黑', 601000.00, 629000.00, 3, '2019-01-04 17:35:18', '1');
INSERT INTO `car` VALUES (19010400005, 5, '2019款 GLC 300 L 4MATIC 豪华型', '皓砂银', 556000.00, 578000.00, 0, '2019-01-04 17:37:32', '1');
INSERT INTO `car` VALUES (19010400006, 6, '2018款 30周年年型 45 TFSI quattro 运动型', '传奇黑', 489000.00, 507000.00, 0, '2018-12-04 17:42:07', '2');
INSERT INTO `car` VALUES (19011200001, 7, '2019款 630i 豪华设计套装', '巴西棕', 585000.00, 599999.00, 1, '2019-01-12 21:37:13', '1');
INSERT INTO `car` VALUES (19011200002, 7, '2019款 630i 豪华设计套装', '神秘灰', 585000.00, 599999.00, 0, '2019-01-12 21:37:42', '2');
INSERT INTO `car` VALUES (19011200003, 7, '2019款 630i M运动套装', '勃艮第红', 622000.00, 639999.00, 2, '2019-01-12 21:39:16', '1');
INSERT INTO `car` VALUES (19011200004, 7, '2019款 630i M运动套装', '炭黑', 622000.00, 639999.00, 2, '2019-01-12 21:39:46', '1');
INSERT INTO `car` VALUES (19011200005, 7, '2019款 630i M运动大旅行家版', '炭黑', 670000.00, 699999.00, 0, '2019-01-12 21:41:37', '2');
INSERT INTO `car` VALUES (19011200006, 8, '2019款 240TURBO CVT两驱舒适版 国V', '珍珠白', 165000.00, 178000.00, 0, '2019-01-12 21:44:07', '2');
INSERT INTO `car` VALUES (19011200007, 8, '2019款 240TURBO CVT两驱舒适版 国V', '彩晶黑', 165000.00, 178000.00, 0, '2019-01-12 21:44:30', '1');
INSERT INTO `car` VALUES (19011200008, 8, '2019款 240TURBO CVT两驱风尚版 国VI', '彩晶黑', 190000.00, 203888.00, 0, '2019-01-12 21:46:01', '1');
INSERT INTO `car` VALUES (19011200009, 8, '2019款 240TURBO CVT四驱尊贵版 国VI', '珍珠白', 216000.00, 238888.00, 2, '2019-01-12 21:47:27', '1');
INSERT INTO `car` VALUES (19011200010, 9, '2018款 260TURBO 精英版', '极光蓝', 168000.00, 186888.00, 0, '2018-11-12 21:52:39', '2');
INSERT INTO `car` VALUES (19011200011, 9, '2018款 260TURBO 精英版', '宝石红', 168000.00, 186888.00, 0, '2018-11-12 21:53:17', '0');
INSERT INTO `car` VALUES (19011200012, 10, '2018款 典藏版 40 TFSI 进取型', '柚木棕', 380000.00, 399999.00, 0, '2019-01-12 21:57:15', '1');
INSERT INTO `car` VALUES (19011200013, 10, '2018款 典藏版 40 TFSI 技术型', '幻影黑', 410000.00, 428000.00, 0, '2019-01-12 21:58:35', '1');
INSERT INTO `car` VALUES (19011200014, 11, '2019款 C 260 运动版', '曜岩黑', 310000.00, 325888.00, 2, '2019-01-12 22:02:07', '1');
INSERT INTO `car` VALUES (19011200015, 11, '2019款 C 260 运动版 4MATIC', '北极白', 365000.00, 383000.00, 0, '2019-01-12 22:03:52', '1');
INSERT INTO `car` VALUES (19011200016, 11, '2019款 C 300 运动版', '浩英石红', 458000.00, 475888.00, 1, '2019-01-12 22:05:09', '1');
INSERT INTO `car` VALUES (19011200017, 12, '2018款 2.0E 领先版', '墨晶黑', 178000.00, 189999.00, 0, '2018-12-12 22:07:31', '0');
INSERT INTO `car` VALUES (19011200018, 12, '2018款 2.5Q 旗舰版', '琥珀棕', 258000.00, 269999.00, 0, '2018-12-12 22:08:49', '0');
INSERT INTO `car` VALUES (19021600002, 13, '2019款 双擎 2.5L 基础版', '传奇黑', 233900.00, 244999.00, 1, '2019-02-16 15:31:40', '1');
INSERT INTO `car` VALUES (19021600003, 13, '2019款 双擎 2.5L 中配版', '传奇黑', 245000.00, 255999.00, 1, '2019-02-16 15:32:48', '1');
INSERT INTO `car` VALUES (19021600004, 13, '2019款 双擎 2.5L 高配版', '传奇黑', 285000.00, 289999.00, 0, '2019-02-16 15:33:31', '2');
INSERT INTO `car` VALUES (19030500001, 8, '2019款 240TURBO CVT两驱舒适版 国V', '琥珀棕', 165000.00, 178000.00, 1, '2019-03-05 20:46:26', '1');
INSERT INTO `car` VALUES (19030500002, 8, '2019款 240TURBO CVT两驱舒适版 国V', '翡玉黑', 165000.00, 178000.00, 2, '2019-03-05 20:47:12', '1');
INSERT INTO `car` VALUES (19030500003, 7, '2019款 630i M运动大旅行家版', '宝石青', 670000.00, 699999.00, 2, '2019-03-05 20:49:58', '1');
INSERT INTO `car` VALUES (19030500004, 14, '2019款 xDrive28i M运动套装', '宝石青', 768000.00, 788888.00, 2, '2019-03-05 20:51:10', '1');
INSERT INTO `car` VALUES (19030500005, 14, '2019款 xDrive35i M运动套装', '宝石青', 848000.00, 868888.00, 2, '2019-03-05 20:52:26', '1');
INSERT INTO `car` VALUES (19041800001, 3, '2019款 540Li 行政版', '月光银', 642000.00, 658888.00, 0, '2019-04-18 19:46:59', '1');
INSERT INTO `car` VALUES (19041800002, 3, '2019款 540Li 行政版', '铂金银', 642000.00, 658888.00, 2, '2019-04-18 19:47:41', '1');
INSERT INTO `car` VALUES (19041800003, 3, '2019款 540Li 行政版', '雪山白', 642000.00, 658888.00, 2, '2019-04-18 19:48:06', '1');
INSERT INTO `car` VALUES (19041800004, 6, '2019款 40 TFSI 豪华致雅型', '传奇黑', 398000.00, 414999.00, 0, '2019-04-18 19:50:20', '1');
INSERT INTO `car` VALUES (19041800005, 6, '2019款 40 TFSI 豪华致雅型', '日珥红', 398000.00, 414999.00, 2, '2019-04-18 19:50:47', '1');
INSERT INTO `car` VALUES (19041800006, 6, '2019款 45 TFSI quattro 尊享动感型', '日珥红', 500000.00, 514999.00, 3, '2019-04-18 19:53:37', '1');
INSERT INTO `car` VALUES (19041800007, 6, '2019款 45 TFSI quattro 尊享动感型', '天云灰', 500000.00, 514999.00, 2, '2019-04-18 19:53:51', '1');
INSERT INTO `car` VALUES (19041900001, 11, '2019款 C 260 L 运动版', '浩英石红', 355000.00, 369999.00, 1, '2019-04-19 20:08:24', '1');
INSERT INTO `car` VALUES (19041900002, 11, '2019款 C 260 L 运动版', '炭灰蓝', 355000.00, 369999.00, 3, '2019-04-19 20:09:00', '1');
INSERT INTO `car` VALUES (19041900003, 12, '2019款 2.0E 领先版 国VI', '墨晶黑', 185000.00, 199999.00, 2, '2019-04-19 20:22:22', '1');
INSERT INTO `car` VALUES (19041900004, 12, '2019款 2.0G 豪华版 国VI', '墨晶黑', 195000.00, 204999.00, 3, '2019-04-19 20:23:03', '1');
INSERT INTO `car` VALUES (19041900005, 12, '2019款 2.5Q 旗舰版 国VI', '琥晴棕', 250000.00, 264999.00, 3, '2019-04-19 20:24:02', '1');
INSERT INTO `car` VALUES (19051400001, 5, '2019款 GLC 200 4MATIC 轿跑SUV', '曜岩黑', 400000.00, 413888.00, 3, '2019-05-14 22:25:16', '1');
INSERT INTO `car` VALUES (19051400002, 5, '2019款 GLC 200 4MATIC 轿跑SUV', '月光石', 400000.00, 413888.00, 3, '2019-05-14 22:25:35', '1');

-- ----------------------------
-- Table structure for customer
-- ----------------------------
DROP TABLE IF EXISTS `customer`;
CREATE TABLE `customer`  (
  `id` bigint(20) NOT NULL COMMENT '顾客编号',
  `name` varchar(10) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '顾客姓名',
  `phone` varchar(11) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '顾客电话',
  `id_card` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '顾客身份证号',
  `create_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '顾客信息创建时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `id_card`(`id_card`) USING BTREE,
  UNIQUE INDEX `phone`(`phone`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of customer
-- ----------------------------
INSERT INTO `customer` VALUES (19010610001, '吴智枫', '18826123456', '268142185206267833', '2019-01-06 10:36:12');
INSERT INTO `customer` VALUES (19010610002, '郭明', '15353831524', '******************', '2019-01-06 10:37:40');
INSERT INTO `customer` VALUES (19010610003, '刘晓芳', '18427537953', '46989818671117261X', '2019-01-06 10:42:38');
INSERT INTO `customer` VALUES (19010610004, '丁绍平', '18621502633', '64596118260416169X', '2019-01-06 10:47:33');
INSERT INTO `customer` VALUES (19010610005, '唐秀兰', '18827966981', '741137183507155509', '2019-01-06 14:08:05');
INSERT INTO `customer` VALUES (19010610006, '赵智勇', '18485226002', '87838525531210087X', '2019-01-06 14:10:14');
INSERT INTO `customer` VALUES (19011010001, '张楚澜', '18361112545', '455568185910039565', '2019-01-10 17:56:44');
INSERT INTO `customer` VALUES (19011010002, '韩娜', '13823116641', '72865118771127336X', '2019-01-10 17:57:14');
INSERT INTO `customer` VALUES (19011010003, '陆俊超', '13492515531', '57456318330225203X', '2019-01-10 20:45:17');
INSERT INTO `customer` VALUES (19011010004, '金昭杰', '18467303561', '61884732051017960X', '2019-01-10 20:46:11');
INSERT INTO `customer` VALUES (19011210001, '龙磊涛', '15428663619', '474416192110205480', '2019-01-12 22:10:01');
INSERT INTO `customer` VALUES (19011210002, '贾天标', '15677257954', '523569225610310935', '2019-01-12 22:10:44');
INSERT INTO `customer` VALUES (19011210003, '傅秀英', '15287390118', '613860251710198501', '2019-01-12 22:11:18');
INSERT INTO `customer` VALUES (19011210004, '文霞', '18553969498', '401168185103032134', '2019-01-12 22:11:37');
INSERT INTO `customer` VALUES (19011210005, '钟桂英', '15526430143', '267632194910266132', '2019-01-12 22:16:02');
INSERT INTO `customer` VALUES (19011210006, '吕元刚', '18475562958', '238186196711155735', '2019-01-12 22:16:49');
INSERT INTO `customer` VALUES (19011210007, '朱应强', '18305595441', '629355229709306317', '2019-01-12 22:21:57');
INSERT INTO `customer` VALUES (19011210008, '赵强军', '13252135624', '654810185602145188', '2019-01-12 22:22:23');
INSERT INTO `customer` VALUES (19011410001, '赵丽霞', '18658609777', '569152188602254364', '2019-01-14 22:34:32');
INSERT INTO `customer` VALUES (19011410002, '彭仁杰', '13577414869', '522538181212108649', '2019-01-14 22:35:12');
INSERT INTO `customer` VALUES (19011410003, '高强', '13472653254', '50220618330310647x', '2019-01-14 22:38:40');
INSERT INTO `customer` VALUES (19011410004, '江蕊娜', '13327364650', '883643197511200340', '2019-01-14 22:39:27');
INSERT INTO `customer` VALUES (19011410005, '曾浩超', '18802938953', '680466182001104121', '2019-01-14 22:44:14');
INSERT INTO `customer` VALUES (19011410006, '曹雪丽', '18506043968', '801231198103185492', '2019-01-14 22:46:07');
INSERT INTO `customer` VALUES (19011610001, '贺珠明', '13487792350', '456315337010313984', '2019-01-16 21:25:49');
INSERT INTO `customer` VALUES (19011610002, '唐平', '15425626505', '49122718920531352X', '2019-01-16 21:26:10');
INSERT INTO `customer` VALUES (19011610003, '董艳', '18583164135', '237331289710102620', '2019-01-16 21:26:31');
INSERT INTO `customer` VALUES (19011610004, '许桂英', '15325758444', '649121192005206722', '2019-01-16 21:34:39');
INSERT INTO `customer` VALUES (19011610005, '何鎏涛', '18681124589', '512872185807124314', '2019-01-16 21:35:43');
INSERT INTO `customer` VALUES (19011610006, '汤秀英', '18894505340', '430522191912102952', '2019-01-16 22:00:29');
INSERT INTO `customer` VALUES (19021610001, '江秀英', '15659532555', '451617244610311883', '2019-02-16 15:07:57');
INSERT INTO `customer` VALUES (19021610002, '邹瑞强', '18552424847', '456617186911203513', '2019-02-16 15:08:29');
INSERT INTO `customer` VALUES (19021610003, '任祥磊', '15271385847', '43450319200220461x', '2019-02-16 15:12:47');
INSERT INTO `customer` VALUES (19021610004, '梁昭朝', '13860030249', '401435218412268021', '2019-02-16 15:13:37');
INSERT INTO `customer` VALUES (19021610005, '邱武艳', '18486577762', '432708186410308540', '2019-02-16 15:14:11');
INSERT INTO `customer` VALUES (19021810001, '张娟', '18443728268', '474531399304193227', '2019-02-18 13:50:11');
INSERT INTO `customer` VALUES (19021810002, '沈芳媛', '18427332510', '673551191602107938', '2019-02-18 14:05:48');
INSERT INTO `customer` VALUES (19030510001, '彭杰勋', '18935394373', '874836182810272272', '2019-03-05 20:30:26');
INSERT INTO `customer` VALUES (19030510002, '叶伟燎', '15269461666', '541208333402105540', '2019-03-05 20:31:01');
INSERT INTO `customer` VALUES (19030510003, '蒋磊炎', '13154238190', '229193192708046401', '2019-03-05 20:31:37');
INSERT INTO `customer` VALUES (19030510004, '邓酥静', '18341654180', '862540188003207383', '2019-03-05 20:32:06');
INSERT INTO `customer` VALUES (19030510005, '董强淑', '18678953706', '447266198211193264', '2019-03-05 20:32:44');
INSERT INTO `customer` VALUES (19030510006, '谢昌平', '18273982724', '493511192810103195', '2019-03-05 20:33:27');
INSERT INTO `customer` VALUES (19041810001, '贺霞', '15854338649', '23539518850210736X', '2019-04-18 19:31:49');
INSERT INTO `customer` VALUES (19041810002, '崔家敏', '18570522655', '87803418211110203X', '2019-04-18 19:32:17');
INSERT INTO `customer` VALUES (19041810003, '秦纬刚', '18918785438', '314870257711108734', '2019-04-18 19:33:00');
INSERT INTO `customer` VALUES (19041810004, '郭筱丽', '15875115297', '876937245605313051', '2019-04-18 19:33:32');
INSERT INTO `customer` VALUES (19041810005, '方芳丽', '13813646893', '395828365411177652', '2019-04-18 19:34:17');
INSERT INTO `customer` VALUES (19041810006, '邱艳枣', '13777339370', '894978188407101335', '2019-04-18 19:34:55');
INSERT INTO `customer` VALUES (19041810007, '朱祖军', '13692521414', '769706189305139236', '2019-04-18 19:38:29');
INSERT INTO `customer` VALUES (19041910001, '李桂英', '15365766813', '762911377202285532', '2019-04-19 19:58:18');
INSERT INTO `customer` VALUES (19041910002, '马银磊', '15611426917', '354745192304147557', '2019-04-19 20:00:04');
INSERT INTO `customer` VALUES (19041910003, '毛涛', '18350317887', '662325188807202758', '2019-04-19 20:00:31');
INSERT INTO `customer` VALUES (19041910004, '夏妍娜', '15405153219', '736793187805126390', '2019-04-19 20:01:05');
INSERT INTO `customer` VALUES (19041910005, '郝镭平', '13369487611', '325526185612145448', '2019-04-19 20:01:33');
INSERT INTO `customer` VALUES (19041910006, '贾召娟', '13631736927', '181613188705105881', '2019-04-19 20:02:06');
INSERT INTO `customer` VALUES (19041910007, '傅明清', '13776625687', '660122188710147033', '2019-04-19 20:04:28');
INSERT INTO `customer` VALUES (19041910008, '冯军耀', '15368161879', '847850183502102939', '2019-04-19 20:26:20');
INSERT INTO `customer` VALUES (19051410001, '田小静', '15925371567', '32278718580614865X', '2019-05-14 22:14:41');
INSERT INTO `customer` VALUES (19051410002, '锺丽娟', '13744385620', '340450196609167529', '2019-05-14 22:15:15');
INSERT INTO `customer` VALUES (19051410003, '魏秀兰', '15522104774', '235318188908203121', '2019-05-14 22:15:42');
INSERT INTO `customer` VALUES (19051410004, '唐康娜', '18827844462', '238251189505126109', '2019-05-14 22:16:07');
INSERT INTO `customer` VALUES (19051410005, '孙尚军', '13863824874', '386614186611104556', '2019-05-14 22:16:37');
INSERT INTO `customer` VALUES (19051410006, '韩熙杰', '15627402841', '681344181510142217', '2019-05-14 22:17:15');

-- ----------------------------
-- Table structure for employee
-- ----------------------------
DROP TABLE IF EXISTS `employee`;
CREATE TABLE `employee`  (
  `id` int(11) NOT NULL COMMENT '员工编号',
  `role` varchar(1) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '员工角色（0：经理，1：销售）',
  `name` varchar(10) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '员工姓名',
  `password` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '员工登录密码',
  `id_card` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '员工身份证号',
  `phone` varchar(11) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '员工电话号码',
  `gender` varchar(1) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '员工性别：\'男\'&\'女\'',
  `salary` decimal(12, 2) NOT NULL COMMENT '员工薪资',
  `entry_time` date NOT NULL COMMENT '员工入职时间',
  `status` varchar(1) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '员工状态（0：离职，1：在职）',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `id_card`(`id_card`) USING BTREE,
  UNIQUE INDEX `phone`(`phone`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of employee
-- ----------------------------
INSERT INTO `employee` VALUES (123456, '0', '马经理', '123456', '******************', '16778125803', '女', 8000.00, '2016-08-19', '1');
INSERT INTO `employee` VALUES (1703001, '1', '邓布利多', '123456', '******************', '18234234672', '男', 5500.00, '2017-03-06', '1');
INSERT INTO `employee` VALUES (1810001, '1', '何熙洋', '123456', '******************', '18152812298', '男', 5000.00, '2018-10-08', '1');
INSERT INTO `employee` VALUES (1810002, '1', '赫敏·格兰杰', '123456', '******************', '18757331422', '女', 5000.00, '2018-10-29', '1');
INSERT INTO `employee` VALUES (1812001, '1', '罗冠杰', '123456', '******************', '15862113190', '男', 5000.00, '2018-12-12', '1');
INSERT INTO `employee` VALUES (1901001, '1', '张晓兰', '123456', '46152119900316571X', '18255406146', '女', 5000.00, '2019-01-08', '1');

-- ----------------------------
-- Table structure for order
-- ----------------------------
DROP TABLE IF EXISTS `order`;
CREATE TABLE `order`  (
  `id` bigint(20) NOT NULL COMMENT '订单编号',
  `customer_id` bigint(20) NOT NULL COMMENT '顾客编号',
  `employee_id` int(11) NOT NULL COMMENT '负责员工编号',
  `total_price` decimal(20, 2) NOT NULL DEFAULT 0.00 COMMENT '订单总金额',
  `status` varchar(1) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '订单状态（0：未支付，1：已支付，2：已取消）',
  `create_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '订单创建时间',
  `pay_time` datetime(0) NULL DEFAULT NULL COMMENT '订单支付时间',
  `update_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '订单更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `customer_id`(`customer_id`) USING BTREE,
  INDEX `employee_id`(`employee_id`) USING BTREE,
  CONSTRAINT `order_ibfk_1` FOREIGN KEY (`customer_id`) REFERENCES `customer` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `order_ibfk_2` FOREIGN KEY (`employee_id`) REFERENCES `employee` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of order
-- ----------------------------
INSERT INTO `order` VALUES (190110000001, 19010610001, 1901001, 2037000.00, '1', '2018-12-10 20:43:06', '2018-12-11 11:13:40', '2018-12-13 21:51:37');
INSERT INTO `order` VALUES (190110000002, 19010610002, 1810002, 578000.00, '1', '2019-01-10 20:43:33', '2019-01-13 10:06:12', '2019-01-13 10:06:12');
INSERT INTO `order` VALUES (190110000003, 19010610003, 1810002, 878000.00, '1', '2019-01-10 20:44:10', '2019-01-10 22:35:55', '2019-01-13 09:52:02');
INSERT INTO `order` VALUES (190110000004, 19010610004, 1901001, 439000.00, '1', '2019-01-10 20:46:52', '2019-01-10 20:46:52', '2019-01-10 20:46:52');
INSERT INTO `order` VALUES (190110000005, 19010610005, 1901001, 629000.00, '2', '2019-01-10 20:47:16', NULL, '2019-01-10 21:02:28');
INSERT INTO `order` VALUES (190110000006, 19010610005, 1901001, 507000.00, '1', '2019-01-10 20:47:29', '2019-01-11 12:23:24', '2019-01-11 12:23:24');
INSERT INTO `order` VALUES (190111000001, 19011010002, 1810001, 629000.00, '1', '2019-01-11 10:59:47', '2019-01-13 10:06:47', '2019-01-13 10:06:47');
INSERT INTO `order` VALUES (190111000002, 19011010001, 123456, 830000.00, '1', '2019-01-11 11:02:37', '2019-01-11 12:18:50', '2019-01-11 12:18:50');
INSERT INTO `order` VALUES (190111000003, 19011010004, 123456, 1017000.00, '2', '2019-01-11 11:13:23', NULL, '2019-01-13 10:10:01');
INSERT INTO `order` VALUES (190111000004, 19011010003, 123456, 523000.00, '1', '2018-12-11 13:25:01', '2018-12-11 13:27:21', '2018-12-13 21:52:32');
INSERT INTO `order` VALUES (190112000001, 19011210004, 1810001, 475888.00, '1', '2019-01-12 22:11:58', '2019-01-12 22:11:58', '2019-01-13 09:52:55');
INSERT INTO `order` VALUES (190112000002, 19011210003, 1703001, 399999.00, '1', '2019-01-12 22:13:07', '2019-01-12 22:13:07', '2019-01-12 22:13:07');
INSERT INTO `order` VALUES (190112000003, 19011210002, 1703001, 376887.00, '1', '2019-01-12 22:14:07', '2019-01-13 10:01:09', '2019-01-13 10:01:09');
INSERT INTO `order` VALUES (190112000004, 19011210001, 1812001, 178000.00, '1', '2018-12-12 22:15:01', '2018-12-12 22:15:01', '2018-12-12 22:15:01');
INSERT INTO `order` VALUES (190112000005, 19010610006, 1812001, 178000.00, '1', '2019-01-12 22:15:24', '2019-01-13 10:06:52', '2019-01-13 10:06:52');
INSERT INTO `order` VALUES (190112000006, 19011210006, 1812001, 269999.00, '1', '2019-01-12 22:17:10', '2019-01-12 22:17:10', '2019-01-12 22:17:10');
INSERT INTO `order` VALUES (190113000001, 19011210005, 1810001, 639999.00, '2', '2019-01-13 10:05:09', NULL, '2019-01-13 10:09:50');
INSERT INTO `order` VALUES (190113000002, 19011210007, 1703001, 699999.00, '1', '2019-01-13 10:08:29', '2019-01-13 10:08:47', '2019-01-13 10:08:47');
INSERT INTO `order` VALUES (190114000001, 19011210008, 1812001, 599999.00, '1', '2019-01-14 13:51:23', '2019-01-14 13:51:23', '2019-01-14 13:51:23');
INSERT INTO `order` VALUES (190114000002, 19011410002, 123456, 376887.00, '1', '2019-01-14 22:37:07', '2019-01-14 22:37:07', '2019-01-14 22:37:07');
INSERT INTO `order` VALUES (190114000003, 19011410001, 1810002, 599999.00, '1', '2019-01-14 22:37:48', '2019-02-16 15:11:39', '2019-02-16 15:11:39');
INSERT INTO `order` VALUES (190114000004, 19011410004, 1901001, 639999.00, '1', '2019-01-14 22:39:54', '2019-01-14 22:39:54', '2019-01-14 22:39:54');
INSERT INTO `order` VALUES (190114000005, 19011410003, 1703001, 439000.00, '1', '2019-01-14 22:42:29', '2019-01-14 22:42:29', '2019-01-14 22:42:29');
INSERT INTO `order` VALUES (190114000006, 19011410005, 1810001, 578000.00, '1', '2019-01-14 22:45:01', '2019-01-14 22:45:01', '2019-01-14 22:45:01');
INSERT INTO `order` VALUES (190114000007, 19011410006, 1810001, 178000.00, '1', '2019-01-14 22:46:26', '2019-01-16 21:32:40', '2019-01-16 21:32:40');
INSERT INTO `order` VALUES (190116000001, 19011610003, 1810001, 186888.00, '1', '2019-01-16 21:27:59', '2019-01-16 21:27:59', '2019-01-16 21:27:59');
INSERT INTO `order` VALUES (190116000002, 19011610002, 1703001, 578000.00, '1', '2019-01-16 21:29:43', '2019-01-16 21:31:21', '2019-01-16 21:31:21');
INSERT INTO `order` VALUES (190116000003, 19011610001, 1810002, 269999.00, '1', '2019-01-16 21:33:36', '2019-01-16 21:33:36', '2019-01-16 21:33:36');
INSERT INTO `order` VALUES (190116000004, 19011610005, 1812001, 439000.00, '1', '2019-01-16 21:36:20', '2019-01-16 21:36:20', '2019-01-16 21:36:20');
INSERT INTO `order` VALUES (190116000005, 19011610004, 1901001, 178000.00, '1', '2019-01-16 21:39:19', '2019-01-16 21:39:19', '2019-01-16 21:39:19');
INSERT INTO `order` VALUES (190116000006, 19011610006, 123456, 830000.00, '1', '2019-01-16 22:00:52', '2019-01-16 22:00:52', '2019-01-16 22:00:52');
INSERT INTO `order` VALUES (190216000001, 19021610002, 123456, 399999.00, '1', '2019-02-16 15:09:52', '2019-02-16 15:09:52', '2019-02-16 15:09:52');
INSERT INTO `order` VALUES (190216000002, 19021610001, 1810002, 1038999.00, '1', '2019-02-16 15:10:46', '2019-02-16 15:10:46', '2019-02-16 15:10:46');
INSERT INTO `order` VALUES (190216000003, 19021610005, 1810001, 903887.00, '1', '2019-02-16 15:15:28', '2019-02-16 15:15:28', '2019-02-16 15:15:28');
INSERT INTO `order` VALUES (190216000004, 19021610004, 1703001, 599999.00, '1', '2019-02-16 15:16:10', '2019-02-16 15:16:10', '2019-02-16 15:16:10');
INSERT INTO `order` VALUES (190216000005, 19021610003, 1812001, 599999.00, '1', '2019-02-16 15:16:53', '2019-02-18 13:43:51', '2019-02-18 13:43:51');
INSERT INTO `order` VALUES (190216000006, 19011210007, 1901001, 178000.00, '1', '2019-02-26 13:17:47', '2019-02-27 10:43:45', '2019-02-28 20:56:03');
INSERT INTO `order` VALUES (190218000001, 19021810001, 123456, 244999.00, '1', '2019-02-26 14:03:09', '2019-02-27 10:03:09', '2019-02-28 20:56:14');
INSERT INTO `order` VALUES (190218000002, 19021810002, 1901001, 203888.00, '1', '2019-02-27 14:06:35', '2019-02-27 14:06:35', '2019-02-28 20:14:24');
INSERT INTO `order` VALUES (190228000001, 19011210003, 123456, 383000.00, '1', '2019-02-28 20:57:32', '2019-02-28 20:57:32', '2019-02-28 20:57:32');
INSERT INTO `order` VALUES (190305000002, 19030510001, 123456, 523000.00, '1', '2019-03-05 20:34:03', '2019-03-05 20:38:40', '2019-03-05 20:38:40');
INSERT INTO `order` VALUES (190305000003, 19030510002, 1703001, 289999.00, '1', '2019-03-05 20:35:16', '2019-03-05 20:35:16', '2019-03-05 20:35:16');
INSERT INTO `order` VALUES (190305000004, 19030510003, 1810001, 428000.00, '1', '2019-03-05 20:36:06', '2019-03-05 20:36:06', '2019-03-05 20:36:06');
INSERT INTO `order` VALUES (190305000005, 19030510004, 1810002, 244999.00, '1', '2019-03-05 20:37:18', '2019-03-05 20:37:18', '2019-03-05 20:37:18');
INSERT INTO `order` VALUES (190305000006, 19030510005, 1812001, 383000.00, '1', '2019-03-05 20:37:48', '2019-03-05 20:37:48', '2019-03-05 20:37:48');
INSERT INTO `order` VALUES (190305000007, 19030510006, 1901001, 178000.00, '1', '2019-03-05 20:38:22', '2019-03-05 20:38:22', '2019-03-05 20:38:22');
INSERT INTO `order` VALUES (190418000001, 19041810001, 123456, 658888.00, '1', '2019-04-18 20:15:46', '2019-04-18 20:15:46', '2019-04-18 20:15:46');
INSERT INTO `order` VALUES (190418000002, 19041810002, 1703001, 514999.00, '1', '2019-04-18 20:17:35', '2019-04-18 20:17:35', '2019-04-18 20:17:35');
INSERT INTO `order` VALUES (190418000003, 19041810003, 1810001, 658888.00, '1', '2019-04-18 20:19:02', '2019-04-18 20:24:18', '2019-04-18 20:24:18');
INSERT INTO `order` VALUES (190418000004, 19041810004, 1810002, 178000.00, '1', '2019-04-18 20:20:36', '2019-04-18 20:20:36', '2019-04-18 20:20:36');
INSERT INTO `order` VALUES (190418000005, 19041810005, 1812001, 658888.00, '1', '2019-04-18 20:21:32', '2019-04-18 20:21:32', '2019-04-18 20:21:32');
INSERT INTO `order` VALUES (190418000006, 19041810006, 1901001, 414999.00, '1', '2019-04-18 20:23:04', '2019-04-18 20:23:04', '2019-04-18 20:23:04');
INSERT INTO `order` VALUES (190419000001, 19041910001, 123456, 547999.00, '1', '2019-04-19 20:11:57', '2019-04-19 20:11:57', '2019-04-19 20:11:57');
INSERT INTO `order` VALUES (190419000002, 19041910002, 1703001, 369999.00, '1', '2019-04-19 20:15:40', '2019-04-19 20:15:40', '2019-04-19 20:15:40');
INSERT INTO `order` VALUES (190419000003, 19041910003, 1810001, 658888.00, '1', '2019-04-19 20:16:44', '2019-04-19 20:16:44', '2019-04-19 20:16:44');
INSERT INTO `order` VALUES (190419000004, 19041910004, 1810002, 428000.00, '1', '2019-04-19 20:17:25', '2019-04-19 20:17:25', '2019-04-19 20:17:25');
INSERT INTO `order` VALUES (190419000005, 19041910005, 1812001, 658888.00, '1', '2019-04-19 20:18:16', '2019-04-19 20:18:16', '2019-04-19 20:18:16');
INSERT INTO `order` VALUES (190419000006, 19041910006, 1901001, 399999.00, '1', '2019-04-19 20:20:24', '2019-04-19 20:20:24', '2019-04-19 20:20:24');
INSERT INTO `order` VALUES (190419000007, 19041910008, 123456, 199999.00, '0', '2019-04-19 20:26:46', NULL, '2019-04-19 20:26:46');
INSERT INTO `order` VALUES (190419000008, 19030510001, 1703001, 178000.00, '0', '2019-04-19 20:27:44', NULL, '2019-04-19 20:27:44');
INSERT INTO `order` VALUES (190420000009, 19010610001, 123456, 853999.00, '0', '2019-04-20 09:59:12', NULL, '2019-04-20 09:59:12');
INSERT INTO `order` VALUES (190514000001, 19051410001, 123456, 414999.00, '1', '2019-05-14 22:18:32', '2019-05-14 22:18:32', '2019-05-14 22:18:32');
INSERT INTO `order` VALUES (190514000002, 19051410002, 1703001, 658888.00, '1', '2019-05-14 22:19:23', '2019-05-14 22:19:23', '2019-05-14 22:19:23');
INSERT INTO `order` VALUES (190514000003, 19051410003, 1810001, 578000.00, '1', '2019-05-14 22:19:52', '2019-05-14 22:19:52', '2019-05-14 22:19:52');
INSERT INTO `order` VALUES (190514000004, 19051410004, 1810002, 414999.00, '1', '2019-05-14 22:20:38', '2019-05-14 22:20:38', '2019-05-14 22:20:38');
INSERT INTO `order` VALUES (190514000005, 19051410005, 1812001, 369999.00, '1', '2019-05-14 22:21:21', '2019-05-14 22:21:21', '2019-05-14 22:21:21');
INSERT INTO `order` VALUES (190514000006, 19051410006, 1901001, 383000.00, '1', '2019-05-14 22:21:53', '2019-05-14 22:21:53', '2019-05-14 22:21:53');

-- ----------------------------
-- Table structure for order_details
-- ----------------------------
DROP TABLE IF EXISTS `order_details`;
CREATE TABLE `order_details`  (
  `id` varchar(16) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '订单详情编号',
  `order_id` bigint(20) NOT NULL COMMENT '订单编号',
  `car_id` bigint(20) NOT NULL COMMENT '车辆编号',
  `car_number` int(11) NOT NULL COMMENT '车辆订单数',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `car_id`(`car_id`) USING BTREE,
  INDEX `order_id`(`order_id`) USING BTREE,
  CONSTRAINT `order_details_ibfk_3` FOREIGN KEY (`car_id`) REFERENCES `car` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `order_details_ibfk_4` FOREIGN KEY (`order_id`) REFERENCES `order` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of order_details
-- ----------------------------
INSERT INTO `order_details` VALUES ('6000000004744459', 190113000002, 19011200005, 1);
INSERT INTO `order_details` VALUES ('6000000111959001', 190110000003, 19010400002, 2);
INSERT INTO `order_details` VALUES ('6000000127116242', 190418000004, 19030500001, 1);
INSERT INTO `order_details` VALUES ('6000000137273228', 190111000003, 19010400002, 1);
INSERT INTO `order_details` VALUES ('6000000189307224', 190216000003, 19011200008, 1);
INSERT INTO `order_details` VALUES ('6000000213204787', 190112000003, 19011200017, 1);
INSERT INTO `order_details` VALUES ('6000000214542331', 190114000006, 19010400005, 1);
INSERT INTO `order_details` VALUES ('6000000247084431', 190116000005, 19011200007, 1);
INSERT INTO `order_details` VALUES ('6000000251012024', 190514000004, 19041800004, 1);
INSERT INTO `order_details` VALUES ('6000000281013309', 190418000002, 19041800007, 1);
INSERT INTO `order_details` VALUES ('6000000307158534', 190514000003, 19010400005, 1);
INSERT INTO `order_details` VALUES ('6000000312040421', 190116000003, 19011200018, 1);
INSERT INTO `order_details` VALUES ('6000000356863651', 190218000002, 19011200008, 1);
INSERT INTO `order_details` VALUES ('6000000361276515', 190114000004, 19011200003, 1);
INSERT INTO `order_details` VALUES ('6000000382903935', 190514000002, 19041800001, 1);
INSERT INTO `order_details` VALUES ('6000000385303251', 190419000004, 19011200013, 1);
INSERT INTO `order_details` VALUES ('6000000397408331', 190116000002, 19010400005, 1);
INSERT INTO `order_details` VALUES ('6000000458200031', 190419000001, 19030500001, 1);
INSERT INTO `order_details` VALUES ('6000000471826418', 190113000001, 19011200003, 1);
INSERT INTO `order_details` VALUES ('6000000472148811', 190216000002, 19010400002, 1);
INSERT INTO `order_details` VALUES ('6000000507283389', 190110000005, 19010400004, 1);
INSERT INTO `order_details` VALUES ('6000000509627231', 190216000001, 19011200012, 1);
INSERT INTO `order_details` VALUES ('6000000539383670', 190116000004, 19010400002, 1);
INSERT INTO `order_details` VALUES ('6000000543879278', 190216000005, 19011200002, 1);
INSERT INTO `order_details` VALUES ('6000000545540431', 190419000003, 19041800003, 1);
INSERT INTO `order_details` VALUES ('6000000548529370', 190419000008, 19030500002, 1);
INSERT INTO `order_details` VALUES ('6000000568241168', 190418000006, 19041800004, 1);
INSERT INTO `order_details` VALUES ('6000000583625725', 190216000003, 19011200005, 1);
INSERT INTO `order_details` VALUES ('6000000671204439', 190305000003, 19021600004, 1);
INSERT INTO `order_details` VALUES ('6000000676891058', 190305000006, 19011200015, 1);
INSERT INTO `order_details` VALUES ('6000000680924492', 190419000001, 19041900001, 1);
INSERT INTO `order_details` VALUES ('6000000744365318', 190111000004, 19010400003, 1);
INSERT INTO `order_details` VALUES ('6000000828039418', 190514000005, 19041900001, 1);
INSERT INTO `order_details` VALUES ('6000000836075569', 190111000003, 19010400005, 1);
INSERT INTO `order_details` VALUES ('6000000886996620', 190419000005, 19041800001, 1);
INSERT INTO `order_details` VALUES ('6000000893271887', 190111000001, 19010400004, 1);
INSERT INTO `order_details` VALUES ('6000000893444257', 190110000001, 19010400001, 1);
INSERT INTO `order_details` VALUES ('6000000894418547', 190216000004, 19011200001, 1);
INSERT INTO `order_details` VALUES ('6000000947106885', 190514000001, 19041800005, 1);
INSERT INTO `order_details` VALUES ('6000000997583297', 190114000002, 19011200017, 1);
INSERT INTO `order_details` VALUES ('6000001003449481', 190420000009, 19010400002, 1);
INSERT INTO `order_details` VALUES ('6000001014087007', 190218000001, 19021600002, 1);
INSERT INTO `order_details` VALUES ('6000001018188807', 190305000005, 19021600002, 1);
INSERT INTO `order_details` VALUES ('6000001038568699', 190419000007, 19041900003, 1);
INSERT INTO `order_details` VALUES ('6000001096382148', 190420000009, 19041800004, 1);
INSERT INTO `order_details` VALUES ('6000001123970316', 190112000001, 19011200016, 1);
INSERT INTO `order_details` VALUES ('6000001141373474', 190419000006, 19011200012, 1);
INSERT INTO `order_details` VALUES ('6000001191299921', 190110000002, 19010400005, 1);
INSERT INTO `order_details` VALUES ('6000001197803655', 190116000001, 19011200010, 1);
INSERT INTO `order_details` VALUES ('6000001207587416', 190305000007, 19011200007, 1);
INSERT INTO `order_details` VALUES ('6000001242742241', 190418000005, 19041800001, 1);
INSERT INTO `order_details` VALUES ('6000001245802911', 190418000003, 19041800002, 1);
INSERT INTO `order_details` VALUES ('6000001311738254', 190305000004, 19011200013, 1);
INSERT INTO `order_details` VALUES ('6000001353793566', 190116000006, 19010400001, 1);
INSERT INTO `order_details` VALUES ('6000001366288746', 190114000003, 19011200002, 1);
INSERT INTO `order_details` VALUES ('6000001372166870', 190112000004, 19011200006, 1);
INSERT INTO `order_details` VALUES ('6000001384357074', 190111000002, 19010400001, 1);
INSERT INTO `order_details` VALUES ('6000001456188231', 190112000003, 19011200010, 1);
INSERT INTO `order_details` VALUES ('6000001458203422', 190110000006, 19010400006, 1);
INSERT INTO `order_details` VALUES ('6000001479188888', 190216000006, 19011200006, 1);
INSERT INTO `order_details` VALUES ('6000001524099322', 190114000001, 19011200001, 1);
INSERT INTO `order_details` VALUES ('6000001678986963', 190114000002, 19011200011, 1);
INSERT INTO `order_details` VALUES ('6000001719057496', 190110000004, 19010400002, 1);
INSERT INTO `order_details` VALUES ('6000001767388823', 190112000005, 19011200007, 1);
INSERT INTO `order_details` VALUES ('6000001774053557', 190228000001, 19011200015, 1);
INSERT INTO `order_details` VALUES ('6000001796191025', 190305000002, 19010400003, 1);
INSERT INTO `order_details` VALUES ('6000001815842703', 190112000002, 19011200012, 1);
INSERT INTO `order_details` VALUES ('6000001872211612', 190110000001, 19010400004, 1);
INSERT INTO `order_details` VALUES ('6000001883484527', 190514000006, 19011200015, 1);
INSERT INTO `order_details` VALUES ('6000001921391675', 190110000001, 19010400005, 1);
INSERT INTO `order_details` VALUES ('6000001928299833', 190112000006, 19011200018, 1);
INSERT INTO `order_details` VALUES ('6000002018161110', 190418000001, 19041800001, 1);
INSERT INTO `order_details` VALUES ('6000002090675180', 190216000002, 19011200002, 1);
INSERT INTO `order_details` VALUES ('6000002133452377', 190419000002, 19041900001, 1);
INSERT INTO `order_details` VALUES ('6000002138376266', 190114000005, 19010400002, 1);
INSERT INTO `order_details` VALUES ('6000002140077836', 190114000007, 19011200006, 1);

-- ----------------------------
-- Table structure for series
-- ----------------------------
DROP TABLE IF EXISTS `series`;
CREATE TABLE `series`  (
  `series_id` int(11) NOT NULL AUTO_INCREMENT COMMENT '车系编号',
  `brand_id` int(11) NOT NULL COMMENT '品牌编号',
  `series_name` varchar(16) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '车系名称',
  `status` varchar(1) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '1' COMMENT '车系状态（0：删除状态，1：正常状态）',
  PRIMARY KEY (`series_id`) USING BTREE,
  INDEX `brand_id`(`brand_id`) USING BTREE,
  CONSTRAINT `series_ibfk_1` FOREIGN KEY (`brand_id`) REFERENCES `brand` (`brand_id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 15 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of series
-- ----------------------------
INSERT INTO `series` VALUES (2, 1, '7系', '1');
INSERT INTO `series` VALUES (3, 1, '5系', '1');
INSERT INTO `series` VALUES (4, 2, 'E级', '1');
INSERT INTO `series` VALUES (5, 2, 'GLC', '1');
INSERT INTO `series` VALUES (6, 3, 'A6L', '1');
INSERT INTO `series` VALUES (7, 1, '6系GT', '1');
INSERT INTO `series` VALUES (8, 4, 'CR-V', '1');
INSERT INTO `series` VALUES (9, 4, '雅阁', '1');
INSERT INTO `series` VALUES (10, 3, 'Q5', '1');
INSERT INTO `series` VALUES (11, 2, 'C级', '1');
INSERT INTO `series` VALUES (12, 5, '凯美瑞', '1');
INSERT INTO `series` VALUES (13, 5, '亚洲龙', '1');
INSERT INTO `series` VALUES (14, 1, 'X6', '1');

SET FOREIGN_KEY_CHECKS = 1;
