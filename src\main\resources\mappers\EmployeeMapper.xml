<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gxyan.dao.EmployeeMapper">
  <resultMap id="BaseResultMap" type="com.gxyan.pojo.Employee">
    <constructor>
      <idArg column="id" javaType="java.lang.Integer" jdbcType="INTEGER" />
      <arg column="role" javaType="java.lang.String" jdbcType="VARCHAR" />
      <arg column="name" javaType="java.lang.String" jdbcType="VARCHAR" />
      <arg column="password" javaType="java.lang.String" jdbcType="VARCHAR" />
      <arg column="id_card" javaType="java.lang.String" jdbcType="VARCHAR" />
      <arg column="phone" javaType="java.lang.String" jdbcType="VARCHAR" />
      <arg column="gender" javaType="java.lang.String" jdbcType="VARCHAR" />
      <arg column="salary" javaType="java.math.BigDecimal" jdbcType="DECIMAL" />
      <arg column="entry_time" javaType="java.util.Date" jdbcType="DATE" />
      <arg column="status" javaType="java.lang.String" jdbcType="VARCHAR" />
    </constructor>
  </resultMap>
  <sql id="Base_Column_List">
    id, role, name, password, id_card, phone, gender, salary, entry_time, status
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from employee
    where id = #{id,jdbcType=INTEGER}
  </select>
  <select id="checkUserId" parameterType="int" resultType="java.lang.Integer">
    select count(1)
    from employee
    where id = #{id}
  </select>
  <select id="selectLogin" resultMap="BaseResultMap" parameterType="map">
    select
    <include refid="Base_Column_List"/>
    from employee
    where id = #{id}
    and password = #{password}
  </select>
  <select id="selectSelective" resultType="com.gxyan.pojo.Employee" parameterType="com.gxyan.vo.EmployeeQuery">
    select
    id, role, name, id_card idCard, phone, gender, salary, entry_time entryTime, status
    from employee
    <where>
      <choose>
        <when test="id != null and id != ''">
          id = #{id}
        </when>
        <when test="phone != null and phone != ''">
          phone = #{phone}
        </when>
        <when test="idCard != null and idCard != ''">
          idCard = #{idCard}
        </when>
        <when test="name != null and name != ''">
          <bind name="pattern" value="'%' + name + '%'"/>
          name like #{pattern}
        </when>
      </choose>
      <if test="status != null and status != ''">
        and status = #{status}
      </if>
    </where>
    <if test="orderBy != null and orderBy != ''">
      order by ${orderBy}
    </if>
  </select>
  <select id="selectPasswordByPrimaryKey" resultType="java.lang.String">
    select password from employee
    where id = #{id}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from employee
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="com.gxyan.pojo.Employee">
    insert into employee (id, role, name, 
      password, id_card, phone, 
      gender, salary, entry_time, 
      status)
    values (#{id,jdbcType=INTEGER}, #{role,jdbcType=VARCHAR}, #{name,jdbcType=VARCHAR},
      #{password,jdbcType=VARCHAR}, #{idCard,jdbcType=VARCHAR}, #{phone,jdbcType=VARCHAR}, 
      #{gender,jdbcType=VARCHAR}, #{salary,jdbcType=DECIMAL}, #{entryTime,jdbcType=DATE},
      #{status,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.gxyan.pojo.Employee">
    insert into employee
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="role != null">
        role,
      </if>
      <if test="name != null">
        name,
      </if>
      <if test="password != null">
        password,
      </if>
      <if test="idCard != null">
        id_card,
      </if>
      <if test="phone != null">
        phone,
      </if>
      <if test="gender != null">
        gender,
      </if>
      <if test="salary != null">
        salary,
      </if>
      <if test="entryTime != null">
        entry_time,
      </if>
      <if test="status != null">
        status,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=INTEGER},
      </if>
      <if test="role != null">
        #{role,jdbcType=VARCHAR},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="password != null">
        #{password,jdbcType=VARCHAR},
      </if>
      <if test="idCard != null">
        #{idCard,jdbcType=VARCHAR},
      </if>
      <if test="phone != null">
        #{phone,jdbcType=VARCHAR},
      </if>
      <if test="gender != null">
        #{gender,jdbcType=VARCHAR},
      </if>
      <if test="salary != null">
        #{salary,jdbcType=DECIMAL},
      </if>
      <if test="entryTime != null">
        #{entryTime,jdbcType=DATE},
      </if>
      <if test="status != null">
        #{status,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.gxyan.pojo.Employee">
    update employee
    <set>
      <if test="role != null">
        role = #{role,jdbcType=VARCHAR},
      </if>
      <if test="name != null">
        name = #{name,jdbcType=VARCHAR},
      </if>
      <if test="password != null">
        password = #{password,jdbcType=VARCHAR},
      </if>
      <if test="idCard != null">
        id_card = #{idCard,jdbcType=VARCHAR},
      </if>
      <if test="phone != null">
        phone = #{phone,jdbcType=VARCHAR},
      </if>
      <if test="gender != null">
        gender = #{gender,jdbcType=VARCHAR},
      </if>
      <if test="salary != null">
        salary = #{salary,jdbcType=DECIMAL},
      </if>
      <if test="entryTime != null">
        entry_time = #{entryTime,jdbcType=DATE},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.gxyan.pojo.Employee">
    update employee
    set role = #{role,jdbcType=VARCHAR},
      name = #{name,jdbcType=VARCHAR},
      password = #{password,jdbcType=VARCHAR},
      id_card = #{idCard,jdbcType=VARCHAR},
      phone = #{phone,jdbcType=VARCHAR},
      gender = #{gender,jdbcType=VARCHAR},
      salary = #{salary,jdbcType=DECIMAL},
      entry_time = #{entryTime,jdbcType=DATE},
      status = #{status,jdbcType=VARCHAR}
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updatePasswordByPrimaryKeyAndOldPass">
    update employee
    set password = #{newPass}
    where id = #{id} and password = #{oldPass}
  </update>
</mapper>