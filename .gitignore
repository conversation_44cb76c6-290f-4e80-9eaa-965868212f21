<<<<<<< HEAD
# Compiled class file
*.class

# Log file
*.log
=======
/target/
!.mvn/wrapper/maven-wrapper.jar

### STS ###
.apt_generated
.classpath
.factorypath
.project
.settings
.springBeans
.sts4-cache

### IntelliJ IDEA ###
.idea
*.iws
*.iml
*.ipr
.mvn
mvnw
mvnw.cmd

### NetBeans ###
/nbproject/private/
/build/
/nbbuild/
/dist/
/nbdist/
/.nb-gradle/

### front ###
.DS_Store
node_modules/
dist/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
**/*.log

test/unit/coverage
test/e2e/reports
selenium-debug.log

# Editor directories and files
.idea
.vscode
*.suo
*.ntvs*
*.njsproj
*.sln

package-lock.json

# Compiled class file
*.class

# temp file
*.log
*.cache
*.diff
*.patch
*.tmp
>>>>>>> for ready

# BlueJ files
*.ctxt

# Mobile Tools for Java (J2ME)
.mtj.tmp/

# Package Files #
<<<<<<< HEAD
*.jar
*.war
*.nar
=======
*.war
>>>>>>> for ready
*.ear
*.zip
*.tar.gz
*.rar

# virtual machine crash logs, see http://www.java.com/en/download/help/error_hotspot.xml
hs_err_pid*
<<<<<<< HEAD
=======

#kdiff3 ignore
*.orig

#maven ignore
target/

#eclipse ignore
.settings/
.project
.classpatch

#idea
.idea/
/idea/
*.ipr
*.iml
*.iws

# system ignore
>>>>>>> for ready
