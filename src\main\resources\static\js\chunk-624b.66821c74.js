(window.webpackJsonp=window.webpackJsonp||[]).push([["chunk-624b"],{"9rC1":function(e,r,t){"use strict";t.d(r,"a",function(){return o}),t.d(r,"b",function(){return u}),t.d(r,"c",function(){return i});var n=t("t3Un");function o(e){return Object(n.a)({url:"/customer/addCustomer",method:"get",params:e})}function u(e){return Object(n.a)({url:"/customer/getList",method:"get",params:e})}function i(e){return Object(n.a)({url:"/customer/update",method:"post",params:e})}},Bfuw:function(e,r,t){var n=t("XdqN"),o=Math.floor;e.exports=function(e){return!n(e)&&isFinite(e)&&o(e)===e}},Yfch:function(e,r,t){"use strict";t.d(r,"a",function(){return u}),t.d(r,"c",function(){return i}),t.d(r,"b",function(){return a}),t.d(r,"d",function(){return s});var n=t("e9eg"),o=t.n(n);function u(e){return/^[1-9]\d{5}(18|19|([23]\d))\d{2}((0[1-9])|(10|11|12))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/.test(e)}var i=function(e,r,t){if(""===r||void 0===r)t(new Error("请输入身份证号"));else{/^[1-9]\d{5}(18|19|([23]\d))\d{2}((0[1-9])|(10|11|12))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/.test(r)||t(new Error("请输入正确的身份证号")),t()}},a=function(e,r,t){if(!r)return t(new Error("年龄不能为空"));setTimeout(function(){o()(r)?r<18?t(new Error("必须年满18岁")):t():t(new Error("请输入数字值"))},1e3)},s=function(e,r,t){if(!r)return t(new Error("请输入员工薪资"));setTimeout(function(){o()(r)?r<1800?t(new Error("薪资必须大于1800元")):t():t(new Error("请输入数字值"))},1e3)}},e9eg:function(e,r,t){e.exports={default:t("t3Qm"),__esModule:!0}},iuhR:function(e,r,t){"use strict";t.r(r);var n=t("Yfch"),o=t("9rC1"),u={data:function(){return{ruleForm:{name:"",phone:"",idCard:""},rules:{name:[{required:!0,message:"请输入客户姓名",trigger:"blur"}],phone:[{required:!0,message:"请输入客户联系电话",trigger:"blur"}],idCard:[{required:!0,validator:n.c,trigger:"blur"}]}}},methods:{submitForm:function(e){var r=this;this.$refs[e].validate(function(t){if(!t)return console.log("error submit!!"),!1;Object(o.a)(r.ruleForm).then(function(t){2e4===t.data.code?r.$notify({title:"成功",message:"添加成功",type:"success",duration:2e3}):r.$notify({title:"错误",message:t.data.message,type:"error",duration:2e3}),r.resetForm(e)})})},resetForm:function(e){this.$refs[e].resetFields()}}},i=t("ZrdR"),a=Object(i.a)(u,function(){var e=this,r=e.$createElement,t=e._self._c||r;return t("div",{staticClass:"app-container"},[t("el-header",[t("h3",{staticStyle:{"font-family":"Microsoft YaHei"}},[e._v("添加客户")]),e._v(" "),t("hr",{staticStyle:{border:"0.5px solid #dcdfe6"}})]),e._v(" "),t("el-form",{ref:"ruleForm",staticClass:"demo-ruleForm",staticStyle:{width:"400px","margin-left":"50px"},attrs:{model:e.ruleForm,rules:e.rules,"label-width":"100px"}},[t("el-form-item",{attrs:{label:"姓名",prop:"name"}},[t("el-input",{model:{value:e.ruleForm.name,callback:function(r){e.$set(e.ruleForm,"name",r)},expression:"ruleForm.name"}})],1),e._v(" "),t("el-form-item",{attrs:{label:"联系电话",prop:"phone"}},[t("el-input",{model:{value:e.ruleForm.phone,callback:function(r){e.$set(e.ruleForm,"phone",r)},expression:"ruleForm.phone"}})],1),e._v(" "),t("el-form-item",{attrs:{label:"身份证号",prop:"idCard"}},[t("el-input",{model:{value:e.ruleForm.idCard,callback:function(r){e.$set(e.ruleForm,"idCard",r)},expression:"ruleForm.idCard"}})],1),e._v(" "),t("el-form-item",[t("el-button",{attrs:{type:"primary"},on:{click:function(r){e.submitForm("ruleForm")}}},[e._v("添加")]),e._v(" "),t("el-button",{on:{click:function(r){e.resetForm("ruleForm")}}},[e._v("重置")])],1)],1)],1)},[],!1,null,null,null);a.options.__file="addCustomer.vue";r.default=a.exports},kmh5:function(e,r,t){var n=t("NCe+");n(n.S,"Number",{isInteger:t("Bfuw")})},t3Qm:function(e,r,t){t("kmh5"),e.exports=t("f4ri").Number.isInteger}}]);