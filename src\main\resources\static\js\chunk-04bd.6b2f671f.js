(window.webpackJsonp=window.webpackJsonp||[]).push([["chunk-04bd"],{"1H4t":function(e,o,t){},"9NmS":function(e,o,t){"use strict";var s=t("1H4t");t.n(s).a},c11S:function(e,o,t){"use strict";var s=t("fN6i");t.n(s).a},fN6i:function(e,o,t){},ntYl:function(e,o,t){"use strict";t.r(o);var s={name:"Login",data:function(){return{loginForm:{roles:"销售",username:"123456",password:"123456"},loginRules:{password:[{required:!0,trigger:"blur",validator:function(e,o,t){o.length<6?t(new Error("The password can not be less than 6 digits")):t()}}]},passwordType:"password",loading:!1,showDialog:!1,redirect:void 0}},watch:{$route:{handler:function(e){this.redirect=e.query&&e.query.redirect},immediate:!0}},methods:{showPwd:function(){"password"===this.passwordType?this.passwordType="":this.passwordType="password"},handleLogin:function(){var e=this;this.$refs.loginForm.validate(function(o){if(!o)return console.log("error submit!!"),!1;e.loading=!0,e.$store.dispatch("LoginByUsername",e.loginForm).then(function(){e.loading=!1,2e4===e.$store.getters.code?e.$router.push({path:e.redirect||"/"}):e.$notify({title:"错误",message:e.$store.getters.message,type:"error",duration:2e3})}).catch(function(){e.loading=!1})})},handleSetRole:function(e){this.loginForm.roles=e,this.$message({message:"Switch roles Success",type:"success"})}}},n=(t("c11S"),t("9NmS"),t("ZrdR")),r=Object(n.a)(s,function(){var e=this,o=e.$createElement,t=e._self._c||o;return t("div",{staticClass:"login-container"},[t("el-form",{ref:"loginForm",staticClass:"login-form",attrs:{model:e.loginForm,rules:e.loginRules,"auto-complete":"on","label-position":"left"}},[t("div",{staticClass:"title-container"},[t("h3",{staticClass:"title"},[e._v("系统登录")]),e._v(" "),t("el-dropdown",{staticClass:"international set-roles",attrs:{trigger:"click"},on:{command:e.handleSetRole}},[t("div",[t("svg-icon",{attrs:{"class-name":"international-icon","icon-class":"admin"}})],1),e._v(" "),t("el-dropdown-menu",{attrs:{slot:"dropdown"},slot:"dropdown"},[t("el-dropdown-item",{attrs:{disabled:"经理"===e.loginForm.roles,command:"经理"}},[e._v("经理")]),e._v(" "),t("el-dropdown-item",{attrs:{disabled:"销售"===e.loginForm.roles,command:"销售"}},[e._v("销售")])],1)],1)],1),e._v(" "),t("el-form-item",{attrs:{prop:"username"}},[t("span",{staticClass:"svg-container"},[t("svg-icon",{attrs:{"icon-class":"user"}})],1),e._v(" "),t("el-input",{attrs:{placeholder:"账号",name:"username",type:"text","auto-complete":"on",required:""},model:{value:e.loginForm.username,callback:function(o){e.$set(e.loginForm,"username",o)},expression:"loginForm.username"}})],1),e._v(" "),t("el-form-item",{attrs:{prop:"password"}},[t("span",{staticClass:"svg-container"},[t("svg-icon",{attrs:{"icon-class":"password"}})],1),e._v(" "),t("el-input",{attrs:{type:e.passwordType,placeholder:"密码",name:"password","auto-complete":"on"},nativeOn:{keyup:function(o){return"button"in o||!e._k(o.keyCode,"enter",13,o.key,"Enter")?e.handleLogin(o):null}},model:{value:e.loginForm.password,callback:function(o){e.$set(e.loginForm,"password",o)},expression:"loginForm.password"}}),e._v(" "),t("span",{staticClass:"show-pwd",on:{click:e.showPwd}},[t("svg-icon",{attrs:{"icon-class":"eye"}})],1)],1),e._v(" "),t("el-button",{staticStyle:{width:"100%","margin-bottom":"30px"},attrs:{loading:e.loading,type:"primary"},nativeOn:{click:function(o){return o.preventDefault(),e.handleLogin(o)}}},[e._v(e._s(e.loginForm.roles)+"登录")])],1)],1)},[],!1,null,"27fd2eb8",null);r.options.__file="index.vue";o.default=r.exports}}]);