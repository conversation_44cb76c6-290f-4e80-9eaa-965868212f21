<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gxyan.dao.OrderDetailsMapper">
  <resultMap id="BaseResultMap" type="com.gxyan.pojo.OrderDetails">
    <constructor>
      <idArg column="id" javaType="java.lang.String" jdbcType="VARCHAR" />
      <arg column="order_id" javaType="java.lang.Long" jdbcType="BIGINT" />
      <arg column="car_id" javaType="java.lang.Long" jdbcType="BIGINT" />
      <arg column="car_number" javaType="java.lang.Integer" jdbcType="INTEGER" />
    </constructor>
  </resultMap>
  <sql id="Base_Column_List">
    id, order_id, car_id, car_number
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from order_details
    where id = #{id,jdbcType=VARCHAR}
  </select>
  <select id="selectByOrderId" resultType="com.gxyan.pojo.OrderDetails">
    select order_details.id, order_id orderId, car_id carId, car_number carNumber
    from order_details
    inner join `order` o on order_details.order_id = o.id
    where order_id = #{orderId}
  </select>
  <select id="selectDetailsByOrderId" resultType="com.gxyan.vo.Details">
    select order_details.id, c.id carId, type, color, sale_price salePrice,
                             b.brand_id brandId, b.brand_name brandName,
                             s.series_id seriesId, s.series_name seriesName,
                             order_details.car_number carNumber
    from order_details
      inner join car c on order_details.car_id = c.id
      inner join series s on c.series_id = s.series_id
      inner join brand b on s.brand_id = b.brand_id
    where order_id = #{orderId}
  </select>
  <select id="selectSelective" resultType="com.gxyan.vo.DetailsList" parameterType="com.gxyan.vo.DetailsQuery">
    select order_details.id, order_details.order_id orderId,
      car_id carId, customer_id customerId,
      e.name employeeName, car_number carNumber,
      create_time createTime, update_time updateTime, o.status
    from order_details
      inner join `order` o on order_details.order_id = o.id
      inner join employee e on o.employee_id = e.id
    <where>
      <choose>
        <when test="id != null and id != ''">
          order_details.id = #{id}
        </when>
        <otherwise>
          <if test="orderId != null and orderId != ''">
            and order_details.order_id = #{orderId}
          </if>
          <if test="customerId != null and customerId != ''">
            and customer_id = #{customerId}
          </if>
          <if test="carId != null and carId != ''">
            and car_id = #{carId}
          </if>
          <if test="employeeName != null and employeeName != ''">
            and e.name = #{employeeName}
          </if>
          <if test="status != null and status != ''">
            and o.status = #{status}
          </if>
        </otherwise>
      </choose>
    </where>
    <if test="orderBy != null and orderBy != ''">
      order by ${orderBy}
    </if>
  </select>
  <select id="selectYesterdayNum" resultType="java.lang.Integer">
    select IFNULL(sum(car_number),0) value from order_details
    INNER JOIN `order` on order_details.order_id = `order`.id
    <![CDATA[
    where TO_DAYS( NOW( ) ) - TO_DAYS(pay_time) < 2 and TO_DAYS( NOW( ) ) - TO_DAYS(pay_time) >= 1
    ]]>
  </select>
  <select id="selectSalesChart" resultType="com.gxyan.vo.SalesChart">
    -- 利润
    SELECT e.date, income, expenditure, income-expenditure profit FROM base_month b
      INNER JOIN (
      -- 收入
        SELECT date,IFNULL(SUM(total_price),0) income FROM base_month
          LEFT JOIN `order` ON date = date_format(pay_time,'%Y-%m')
        <![CDATA[
        WHERE date <= #{end} AND date >= #{start}
        ]]>
        GROUP BY date
      ) i ON i.date = b.date
      INNER JOIN (
        -- 支出
        SELECT date,IFNULL(SUM((repertory+IFNULL(num,0))*price),0) expenditure FROM base_month
          LEFT JOIN car ON date = date_format(create_time,'%Y-%m')
          LEFT JOIN (
            SELECT car_id,SUM(car_number) num FROM order_details
            GROUP BY car_id
          ) o on o.car_id = car.id
        <![CDATA[
        WHERE date <= #{end} AND date >= #{start}
        ]]>
        GROUP BY date
      ) e ON e.date = b.date
    GROUP BY e.date
  </select>
  <select id="selectIndexSales" resultType="com.gxyan.vo.IndexSales" parameterType="int">
    SELECT SUM(total_price) sales,SUM(car_number) saleNum FROM `order`
      INNER JOIN order_details ON order_id = `order`.id
    WHERE DATE_FORMAT(pay_time,'%Y-%m') = DATE_FORMAT(NOW(),'%Y-%m')
          AND employee_id = #{id}
  </select>
  <select id="selectEmpChart" resultType="com.gxyan.vo.EmpChart">
    SELECT CONCAT_WS('/',brand.brand_name,series.series_name) name, SUM(num*sale_price) value FROM car
      INNER JOIN (
                   SELECT car_id,SUM(car_number) num FROM order_details
                     INNER JOIN `order` ON order_details.order_id = `order`.id
                   WHERE DATE_FORMAT(pay_time,'%Y-%m') = #{date}
                         AND employee_id = #{id}
                   GROUP BY car_id
                 ) o ON car.id = o.car_id
      INNER JOIN series ON car.series_id = series.series_id
      INNER JOIN brand ON series.brand_id = brand.brand_id
    GROUP BY series.series_id
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    delete from order_details
    where id = #{id,jdbcType=VARCHAR}
  </delete>
  <insert id="insert" parameterType="com.gxyan.pojo.OrderDetails">
    insert into order_details (id, order_id, car_id, 
      car_number)
    values (#{id,jdbcType=VARCHAR}, #{orderId,jdbcType=BIGINT}, #{carId,jdbcType=BIGINT},
      #{carNumber,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" parameterType="com.gxyan.pojo.OrderDetails">
    insert into order_details
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="orderId != null">
        order_id,
      </if>
      <if test="carId != null">
        car_id,
      </if>
      <if test="carNumber != null">
        car_number,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="orderId != null">
        #{orderId,jdbcType=BIGINT},
      </if>
      <if test="carId != null">
        #{carId,jdbcType=BIGINT},
      </if>
      <if test="carNumber != null">
        #{carNumber,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.gxyan.pojo.OrderDetails">
    update order_details
    <set>
      <if test="orderId != null">
        order_id = #{orderId,jdbcType=BIGINT},
      </if>
      <if test="carId != null">
        car_id = #{carId,jdbcType=BIGINT},
      </if>
      <if test="carNumber != null">
        car_number = #{carNumber,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.gxyan.pojo.OrderDetails">
    update order_details
    set order_id = #{orderId,jdbcType=BIGINT},
      car_id = #{carId,jdbcType=BIGINT},
      car_number = #{carNumber,jdbcType=INTEGER}
    where id = #{id,jdbcType=VARCHAR}
  </update>
</mapper>