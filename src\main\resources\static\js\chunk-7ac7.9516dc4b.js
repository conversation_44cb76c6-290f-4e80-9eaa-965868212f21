(window.webpackJsonp=window.webpackJsonp||[]).push([["chunk-7ac7"],{"+Lc1":function(e,t,n){"use strict";n.d(t,"a",function(){return o}),n.d(t,"d",function(){return l}),n.d(t,"f",function(){return s}),n.d(t,"e",function(){return u}),n.d(t,"b",function(){return c}),n.d(t,"c",function(){return d});var a=n("omC7"),i=n.n(a),r=n("t3Un");function o(e){return Object(r.a)({headers:{Accept:"application/json","Content-Type":"application/json"},url:"/order/addOrder",method:"post",data:i()(e)})}function l(e){return Object(r.a)({url:"/order/getList",method:"get",params:e})}function s(e){return Object(r.a)({url:"/order/update",method:"post",params:e})}function u(e){return Object(r.a)({url:"/order/updateDetail",method:"post",params:e})}function c(e){return Object(r.a)({url:"/order/deleteDetail",method:"post",params:e})}function d(e){return Object(r.a)({url:"/order/getDetailsList",method:"get",params:e})}},"1Bvf":function(e,t,n){},"69Nf":function(e,t,n){var a=n("f4ri"),i=a.JSON||(a.JSON={stringify:JSON.stringify});e.exports=function(e){return i.stringify.apply(i,arguments)}},"K/D6":function(e,t,n){"use strict";var a=n("1Bvf");n.n(a).a},Mz3J:function(e,t,n){"use strict";Math.easeInOutQuad=function(e,t,n,a){return(e/=a/2)<1?n/2*e*e+t:-n/2*(--e*(e-2)-1)+t};var a=window.requestAnimationFrame||window.webkitRequestAnimationFrame||window.mozRequestAnimationFrame||function(e){window.setTimeout(e,1e3/60)};function i(e,t,n){var i=document.documentElement.scrollTop||document.body.parentNode.scrollTop||document.body.scrollTop,r=e-i,o=0;t=void 0===t?500:t;!function e(){o+=20,function(e){document.documentElement.scrollTop=e,document.body.parentNode.scrollTop=e,document.body.scrollTop=e}(Math.easeInOutQuad(o,i,r,t)),o<t?a(e):n&&"function"==typeof n&&n()}()}var r={name:"Pagination",props:{total:{required:!0,type:Number},page:{type:Number,default:1},limit:{type:Number,default:5},pageSizes:{type:Array,default:function(){return[5,10,20,50]}},layout:{type:String,default:"total, sizes, prev, pager, next, jumper"},background:{type:Boolean,default:!0},autoScroll:{type:Boolean,default:!0},hidden:{type:Boolean,default:!1}},computed:{currentPage:{get:function(){return this.page},set:function(e){this.$emit("update:page",e)}},pageSize:{get:function(){return this.limit},set:function(e){this.$emit("update:limit",e)}}},methods:{handleSizeChange:function(e){this.$emit("pagination",{page:this.currentPage,limit:e}),this.autoScroll&&i(0,800)},handleCurrentChange:function(e){this.$emit("pagination",{page:e,limit:this.pageSize}),this.autoScroll&&i(0,800)}}},o=(n("K/D6"),n("ZrdR")),l=Object(o.a)(r,function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticClass:"pagination-container",class:{hidden:e.hidden}},[n("el-pagination",e._b({attrs:{background:e.background,"current-page":e.currentPage,"page-size":e.pageSize,layout:e.layout,"page-sizes":e.pageSizes,total:e.total},on:{"update:currentPage":function(t){e.currentPage=t},"update:pageSize":function(t){e.pageSize=t},"size-change":e.handleSizeChange,"current-change":e.handleCurrentChange}},"el-pagination",e.$attrs,!1))],1)},[],!1,null,"6fb7ae22",null);l.options.__file="index.vue";t.a=l.exports},ZySA:function(e,t,n){"use strict";var a=n("6ZY3"),i=n.n(a),r=(n("jUE0"),{bind:function(e,t){e.addEventListener("click",function(n){var a=i()({},t.value),r=i()({ele:e,type:"hit",color:"rgba(0, 0, 0, 0.15)"},a),o=r.ele;if(o){o.style.position="relative",o.style.overflow="hidden";var l=o.getBoundingClientRect(),s=o.querySelector(".waves-ripple");switch(s?s.className="waves-ripple":((s=document.createElement("span")).className="waves-ripple",s.style.height=s.style.width=Math.max(l.width,l.height)+"px",o.appendChild(s)),r.type){case"center":s.style.top=l.height/2-s.offsetHeight/2+"px",s.style.left=l.width/2-s.offsetWidth/2+"px";break;default:s.style.top=(n.pageY-l.top-s.offsetHeight/2-document.documentElement.scrollTop||document.body.scrollTop)+"px",s.style.left=(n.pageX-l.left-s.offsetWidth/2-document.documentElement.scrollLeft||document.body.scrollLeft)+"px"}return s.style.backgroundColor=r.color,s.className="waves-ripple z-active",!1}},!1)}}),o=function(e){e.directive("waves",r)};window.Vue&&(window.waves=r,Vue.use(o)),r.install=o;t.a=r},jUE0:function(e,t,n){},omC7:function(e,t,n){e.exports={default:n("69Nf"),__esModule:!0}},"zD3/":function(e,t,n){"use strict";n.r(t);var a=n("+Lc1"),i=n("ZySA"),r=n("7Qib"),o=n("Mz3J"),l=[{key:"0",display_name:"未支付"},{key:"1",display_name:"已支付"},{key:"2",display_name:"已取消"}],s=l.reduce(function(e,t){return e[t.key]=t.display_name,e},{}),u={components:{Pagination:o.a},directives:{waves:i.a},filters:{statusFilter:function(e){return{0:"danger",1:"success",2:"info"}[e]},typeFilter:function(e){return s[e]}},data:function(){return{tableKey:0,list:null,total:0,listLoading:!0,listQuery:{page:1,limit:5,id:void 0,orderId:void 0,customerId:void 0,carId:void 0,employeeName:void 0,status:void 0,orderBy:"updateTime desc"},orderStatusOptions:l,downloadLoading:!1}},created:function(){this.getList()},methods:{getList:function(){var e=this;this.listLoading=!0,Object(a.c)(this.listQuery).then(function(t){2e4===t.data.code?(e.list=t.data.data.items,e.total=t.data.data.total,e.listLoading=!1):(e.$message({message:t.data.message,type:"error"}),e.listLoading=!1)})},handleFilter:function(){this.listQuery.page=1,this.getList()},handleModifyStatus:function(e,t){this.$message({message:"操作成功",type:"success"}),e.status=t},sortChange:function(e){var t=e.prop,n=e.order;this.listQuery.orderBy="ascending"===n?t+" asc":t+" desc",this.handleFilter()},handleDownload:function(){var e=this;this.downloadLoading=!0,Promise.all([n.e("chunk-3806"),n.e("chunk-5556")]).then(n.bind(null,"S/jZ")).then(function(t){var n=e.formatJson(["id","orderId","customerId","carId","carNumber","employeeName","createTime","updateTime","status"],e.list);t.export_json_to_excel({header:["订单详情编号","订单编号","客户编号","车辆编号","数量","销售员","创建时间","更新时间","状态"],data:n,filename:"订单详情"}),e.downloadLoading=!1})},formatJson:function(e,t){return t.map(function(t){return e.map(function(e){return"createTime"===e?Object(r.d)(t[e]):"updateTime"===e?Object(r.d)(t[e]):"status"===e?s[t[e]]:t[e]})})}}},c=n("ZrdR"),d=Object(c.a)(u,function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticClass:"app-container"},[n("div",{staticClass:"filter-container"},[n("el-input",{staticClass:"filter-item",staticStyle:{width:"160px"},attrs:{placeholder:"订单详情编号"},nativeOn:{keyup:function(t){return"button"in t||!e._k(t.keyCode,"enter",13,t.key,"Enter")?e.handleFilter(t):null}},model:{value:e.listQuery.id,callback:function(t){e.$set(e.listQuery,"id",t)},expression:"listQuery.id"}}),e._v(" "),n("el-input",{staticClass:"filter-item",staticStyle:{width:"160px"},attrs:{placeholder:"订单编号"},nativeOn:{keyup:function(t){return"button"in t||!e._k(t.keyCode,"enter",13,t.key,"Enter")?e.handleFilter(t):null}},model:{value:e.listQuery.orderId,callback:function(t){e.$set(e.listQuery,"orderId",t)},expression:"listQuery.orderId"}}),e._v(" "),n("el-input",{staticClass:"filter-item",staticStyle:{width:"160px"},attrs:{placeholder:"客户编号"},nativeOn:{keyup:function(t){return"button"in t||!e._k(t.keyCode,"enter",13,t.key,"Enter")?e.handleFilter(t):null}},model:{value:e.listQuery.customerId,callback:function(t){e.$set(e.listQuery,"customerId",t)},expression:"listQuery.customerId"}}),e._v(" "),n("el-input",{staticClass:"filter-item",staticStyle:{width:"160px"},attrs:{placeholder:"车辆编号"},nativeOn:{keyup:function(t){return"button"in t||!e._k(t.keyCode,"enter",13,t.key,"Enter")?e.handleFilter(t):null}},model:{value:e.listQuery.carId,callback:function(t){e.$set(e.listQuery,"carId",t)},expression:"listQuery.carId"}}),e._v(" "),n("el-input",{staticClass:"filter-item",staticStyle:{width:"110px"},attrs:{placeholder:"销售员"},nativeOn:{keyup:function(t){return"button"in t||!e._k(t.keyCode,"enter",13,t.key,"Enter")?e.handleFilter(t):null}},model:{value:e.listQuery.employeeName,callback:function(t){e.$set(e.listQuery,"employeeName",t)},expression:"listQuery.employeeName"}}),e._v(" "),n("el-select",{staticClass:"filter-item",staticStyle:{width:"110px"},attrs:{placeholder:"状态",clearable:""},nativeOn:{keyup:function(t){return"button"in t||!e._k(t.keyCode,"enter",13,t.key,"Enter")?e.handleFilter(t):null}},model:{value:e.listQuery.status,callback:function(t){e.$set(e.listQuery,"status",t)},expression:"listQuery.status"}},e._l(e.orderStatusOptions,function(e){return n("el-option",{key:e.key,attrs:{label:e.display_name,value:e.key}})})),e._v(" "),n("el-button",{directives:[{name:"waves",rawName:"v-waves"}],staticClass:"filter-item",attrs:{type:"primary",icon:"el-icon-search"},on:{click:e.handleFilter}},[e._v("搜索")]),e._v(" "),n("el-button",{directives:[{name:"waves",rawName:"v-waves"}],staticClass:"filter-item",attrs:{loading:e.downloadLoading,type:"primary",icon:"el-icon-download"},on:{click:e.handleDownload}},[e._v("导出")])],1),e._v(" "),n("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.listLoading,expression:"listLoading"}],key:e.tableKey,staticStyle:{width:"100%"},attrs:{data:e.list,border:"",fit:"","highlight-current-row":""},on:{"sort-change":e.sortChange}},[n("el-table-column",{attrs:{label:"订单详情编号",prop:"id",sortable:"custom",align:"center","min-width":"125px"},scopedSlots:e._u([{key:"default",fn:function(t){return[n("span",[e._v(e._s(t.row.id))])]}}])}),e._v(" "),n("el-table-column",{attrs:{label:"订单编号",prop:"orderId",sortable:"custom",align:"center","min-width":"105px"},scopedSlots:e._u([{key:"default",fn:function(t){return[n("span",[e._v(e._s(t.row.orderId))])]}}])}),e._v(" "),n("el-table-column",{attrs:{label:"客户编号",prop:"customerId",sortable:"custom",align:"center","min-width":"105px"},scopedSlots:e._u([{key:"default",fn:function(t){return[n("span",[e._v(e._s(t.row.customerId))])]}}])}),e._v(" "),n("el-table-column",{attrs:{label:"车辆编号",prop:"carId",sortable:"custom",align:"center","min-width":"105px"},scopedSlots:e._u([{key:"default",fn:function(t){return[n("span",[e._v(e._s(t.row.carId))])]}}])}),e._v(" "),n("el-table-column",{attrs:{label:"数量",prop:"carNumber",align:"center","min-width":"50px"},scopedSlots:e._u([{key:"default",fn:function(t){return[n("span",[e._v(e._s(t.row.carNumber))])]}}])}),e._v(" "),n("el-table-column",{attrs:{label:"销售员",width:"80px",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[n("span",[e._v(e._s(t.row.employeeName))])]}}])}),e._v(" "),n("el-table-column",{attrs:{label:"创建时间",prop:"createTime",sortable:"custom","min-width":"135px",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[n("span",[e._v(e._s(e._f("parseTime")(t.row.createTime,"{y}-{m}-{d} {h}:{i}")))])]}}])}),e._v(" "),n("el-table-column",{attrs:{label:"更新时间",prop:"updateTime",sortable:"custom","min-width":"135px",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[n("span",[e._v(e._s(e._f("parseTime")(t.row.updateTime,"{y}-{m}-{d} {h}:{i}")))])]}}])}),e._v(" "),n("el-table-column",{attrs:{label:"状态","class-name":"status-col","min-width":"95px"},scopedSlots:e._u([{key:"default",fn:function(t){return[n("el-tag",{attrs:{type:e._f("statusFilter")(t.row.status)}},[e._v(e._s(e._f("typeFilter")(t.row.status)))])]}}])})],1),e._v(" "),n("pagination",{directives:[{name:"show",rawName:"v-show",value:e.total>0,expression:"total>0"}],attrs:{total:e.total,page:e.listQuery.page,limit:e.listQuery.limit},on:{"update:page":function(t){e.$set(e.listQuery,"page",t)},"update:limit":function(t){e.$set(e.listQuery,"limit",t)},pagination:e.getList}})],1)},[],!1,null,null,null);d.options.__file="orderDetails.vue";t.default=d.exports}}]);