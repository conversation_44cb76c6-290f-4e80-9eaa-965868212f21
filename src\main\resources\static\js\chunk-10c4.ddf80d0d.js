(window.webpackJsonp=window.webpackJsonp||[]).push([["chunk-10c4"],{"1Bvf":function(e,t,n){},"73Xj":function(e,t,n){"use strict";n.d(t,"a",function(){return i}),n.d(t,"b",function(){return r}),n.d(t,"c",function(){return l});var a=n("t3Un");function i(e){return console.log(e),Object(a.a)({url:"/employee/addEmployee",method:"post",params:e})}function r(e){return console.log(e),Object(a.a)({url:"/employee/getList",method:"get",params:e})}function l(e){return console.log(e),Object(a.a)({url:"/employee/update",method:"post",params:e})}},Bfuw:function(e,t,n){var a=n("XdqN"),i=Math.floor;e.exports=function(e){return!a(e)&&isFinite(e)&&i(e)===e}},"K/D6":function(e,t,n){"use strict";var a=n("1Bvf");n.n(a).a},Mz3J:function(e,t,n){"use strict";Math.easeInOutQuad=function(e,t,n,a){return(e/=a/2)<1?n/2*e*e+t:-n/2*(--e*(e-2)-1)+t};var a=window.requestAnimationFrame||window.webkitRequestAnimationFrame||window.mozRequestAnimationFrame||function(e){window.setTimeout(e,1e3/60)};function i(e,t,n){var i=document.documentElement.scrollTop||document.body.parentNode.scrollTop||document.body.scrollTop,r=e-i,l=0;t=void 0===t?500:t;!function e(){l+=20,function(e){document.documentElement.scrollTop=e,document.body.parentNode.scrollTop=e,document.body.scrollTop=e}(Math.easeInOutQuad(l,i,r,t)),l<t?a(e):n&&"function"==typeof n&&n()}()}var r={name:"Pagination",props:{total:{required:!0,type:Number},page:{type:Number,default:1},limit:{type:Number,default:5},pageSizes:{type:Array,default:function(){return[5,10,20,50]}},layout:{type:String,default:"total, sizes, prev, pager, next, jumper"},background:{type:Boolean,default:!0},autoScroll:{type:Boolean,default:!0},hidden:{type:Boolean,default:!1}},computed:{currentPage:{get:function(){return this.page},set:function(e){this.$emit("update:page",e)}},pageSize:{get:function(){return this.limit},set:function(e){this.$emit("update:limit",e)}}},methods:{handleSizeChange:function(e){this.$emit("pagination",{page:this.currentPage,limit:e}),this.autoScroll&&i(0,800)},handleCurrentChange:function(e){this.$emit("pagination",{page:e,limit:this.pageSize}),this.autoScroll&&i(0,800)}}},l=(n("K/D6"),n("ZrdR")),o=Object(l.a)(r,function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticClass:"pagination-container",class:{hidden:e.hidden}},[n("el-pagination",e._b({attrs:{background:e.background,"current-page":e.currentPage,"page-size":e.pageSize,layout:e.layout,"page-sizes":e.pageSizes,total:e.total},on:{"update:currentPage":function(t){e.currentPage=t},"update:pageSize":function(t){e.pageSize=t},"size-change":e.handleSizeChange,"current-change":e.handleCurrentChange}},"el-pagination",e.$attrs,!1))],1)},[],!1,null,"6fb7ae22",null);o.options.__file="index.vue";t.a=o.exports},Yfch:function(e,t,n){"use strict";n.d(t,"a",function(){return r}),n.d(t,"c",function(){return l}),n.d(t,"b",function(){return o}),n.d(t,"d",function(){return s});var a=n("e9eg"),i=n.n(a);function r(e){return/^[1-9]\d{5}(18|19|([23]\d))\d{2}((0[1-9])|(10|11|12))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/.test(e)}var l=function(e,t,n){if(""===t||void 0===t)n(new Error("请输入身份证号"));else{/^[1-9]\d{5}(18|19|([23]\d))\d{2}((0[1-9])|(10|11|12))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/.test(t)||n(new Error("请输入正确的身份证号")),n()}},o=function(e,t,n){if(!t)return n(new Error("年龄不能为空"));setTimeout(function(){i()(t)?t<18?n(new Error("必须年满18岁")):n():n(new Error("请输入数字值"))},1e3)},s=function(e,t,n){if(!t)return n(new Error("请输入员工薪资"));setTimeout(function(){i()(t)?t<1800?n(new Error("薪资必须大于1800元")):n():n(new Error("请输入数字值"))},1e3)}},ZUgX:function(e,t,n){"use strict";n.r(t);var a=n("rerW"),i=n.n(a),r=n("6ZY3"),l=n.n(r),o=n("ZySA"),s=n("7Qib"),u=n("Mz3J"),d=n("Yfch"),c=n("73Xj"),p=[{key:"1",display_name:"在职"},{key:"0",display_name:"已离职"}],m=p.reduce(function(e,t){return e[t.key]=t.display_name,e},{}),f={components:{Pagination:u.a},directives:{waves:o.a},filters:{statusFilter:function(e){return{0:"danger",1:"success"}[e]},typeFilter:function(e){return m[e]}},data:function(){return{tableKey:0,list:null,total:0,listLoading:!0,listQuery:{page:1,limit:5,id:void 0,name:void 0,phone:void 0,idCard:void 0,status:void 0,orderBy:void 0},statusOptions:p,temp:{id:void 0,name:"",phone:"",idCard:"",gender:void 0,salary:0,status:void 0},dialogFormVisible:!1,downloadLoading:!1,rules:{name:[{required:!0,message:"请输入员工姓名",trigger:"blur"}],phone:[{required:!0,message:"请输入员工联系电话",trigger:"blur"}],idCard:[{required:!0,validator:d.c,trigger:"blur"}],gender:[{required:!0,message:"请选择员工性别",trigger:"change"}],status:[{required:!0,message:"请选择员工就职状态",trigger:"blur"}],salary:[{validator:d.d,required:!0,trigger:"blur"}]}}},created:function(){this.getList()},methods:{getList:function(){var e=this;this.listLoading=!0,Object(c.b)(this.listQuery).then(function(t){2e4===t.data.code?(e.list=t.data.data.items,e.total=t.data.data.total):e.$message({message:t.data.message,type:"error"}),e.listLoading=!1})},handleFilter:function(){this.listQuery.page=1,this.getList()},sortChange:function(e){var t=e.prop,n=e.order;this.listQuery.orderBy="ascending"===n?t+" asc":t+" desc",this.handleFilter()},handleUpdate:function(e){var t=this;this.temp=l()({},e),this.dialogFormVisible=!0,this.$nextTick(function(){t.$refs.dataForm.clearValidate()})},updateData:function(){var e=this;this.$refs.dataForm.validate(function(t){if(t){var n=l()({},e.temp);n.entryTime=null,Object(c.c)(n).then(function(t){if(2e4===t.data.code){var n=!0,a=!1,r=void 0;try{for(var l,o=i()(e.list);!(n=(l=o.next()).done);n=!0){var s=l.value;if(s.id===e.temp.id){var u=e.list.indexOf(s);e.list.splice(u,1,e.temp);break}}}catch(e){a=!0,r=e}finally{try{!n&&o.return&&o.return()}finally{if(a)throw r}}e.dialogFormVisible=!1,e.$notify({title:"成功",message:"更新成功",type:"success",duration:2e3})}else e.$notify({title:"错误",message:t.data.message,type:"error",duration:2e3})})}})},handleDownload:function(){var e=this;this.downloadLoading=!0,Promise.all([n.e("chunk-3806"),n.e("chunk-5556")]).then(n.bind(null,"S/jZ")).then(function(t){var n=e.formatJson(["timestamp","title","type","importance","status"],e.list);t.export_json_to_excel({header:["timestamp","title","type","importance","status"],data:n,filename:"table-list"}),e.downloadLoading=!1})},formatJson:function(e,t){return t.map(function(t){return e.map(function(e){return"timestamp"===e?Object(s.d)(t[e]):t[e]})})}}},y=n("ZrdR"),g=Object(y.a)(f,function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticClass:"app-container"},[n("div",{staticClass:"filter-container"},[n("el-input",{staticClass:"filter-item",staticStyle:{width:"160px"},attrs:{placeholder:"员工编号"},nativeOn:{keyup:function(t){return"button"in t||!e._k(t.keyCode,"enter",13,t.key,"Enter")?e.handleFilter(t):null}},model:{value:e.listQuery.id,callback:function(t){e.$set(e.listQuery,"id",t)},expression:"listQuery.id"}}),e._v(" "),n("el-input",{staticClass:"filter-item",staticStyle:{width:"150px"},attrs:{placeholder:"员工姓名"},nativeOn:{keyup:function(t){return"button"in t||!e._k(t.keyCode,"enter",13,t.key,"Enter")?e.handleFilter(t):null}},model:{value:e.listQuery.name,callback:function(t){e.$set(e.listQuery,"name",t)},expression:"listQuery.name"}}),e._v(" "),n("el-input",{staticClass:"filter-item",staticStyle:{width:"200px"},attrs:{placeholder:"身份证号"},nativeOn:{keyup:function(t){return"button"in t||!e._k(t.keyCode,"enter",13,t.key,"Enter")?e.handleFilter(t):null}},model:{value:e.listQuery.idCard,callback:function(t){e.$set(e.listQuery,"idCard",t)},expression:"listQuery.idCard"}}),e._v(" "),n("el-input",{staticClass:"filter-item",staticStyle:{width:"160px"},attrs:{placeholder:"电话号码"},nativeOn:{keyup:function(t){return"button"in t||!e._k(t.keyCode,"enter",13,t.key,"Enter")?e.handleFilter(t):null}},model:{value:e.listQuery.phone,callback:function(t){e.$set(e.listQuery,"phone",t)},expression:"listQuery.phone"}}),e._v(" "),n("el-select",{staticClass:"filter-item",staticStyle:{width:"110px"},attrs:{placeholder:"状态",clearable:""},nativeOn:{keyup:function(t){return"button"in t||!e._k(t.keyCode,"enter",13,t.key,"Enter")?e.handleFilter(t):null}},model:{value:e.listQuery.status,callback:function(t){e.$set(e.listQuery,"status",t)},expression:"listQuery.status"}},e._l(e.statusOptions,function(e){return n("el-option",{key:e.key,attrs:{label:e.display_name,value:e.key}})})),e._v(" "),n("el-button",{directives:[{name:"waves",rawName:"v-waves"}],staticClass:"filter-item",attrs:{type:"primary",icon:"el-icon-search"},on:{click:e.handleFilter}},[e._v("搜索")]),e._v(" "),n("el-button",{directives:[{name:"waves",rawName:"v-waves"}],staticClass:"filter-item",attrs:{loading:e.downloadLoading,type:"primary",icon:"el-icon-download"},on:{click:e.handleDownload}},[e._v("导出")])],1),e._v(" "),n("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.listLoading,expression:"listLoading"}],key:e.tableKey,staticStyle:{width:"100%"},attrs:{data:e.list,border:"",fit:"","highlight-current-row":""},on:{"sort-change":e.sortChange}},[n("el-table-column",{attrs:{label:"员工编号",prop:"id",sortable:"custom",align:"center","min-width":"105px"},scopedSlots:e._u([{key:"default",fn:function(t){return[n("span",[e._v(e._s(t.row.id))])]}}])}),e._v(" "),n("el-table-column",{attrs:{label:"姓名","min-width":"80px",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[n("span",[e._v(e._s(t.row.name))])]}}])}),e._v(" "),n("el-table-column",{attrs:{label:"电话号码","min-width":"115px",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[n("span",[e._v(e._s(t.row.phone))])]}}])}),e._v(" "),n("el-table-column",{attrs:{label:"性别",align:"center","min-width":"50px"},scopedSlots:e._u([{key:"default",fn:function(t){return[n("span",[e._v(e._s(t.row.gender))])]}}])}),e._v(" "),n("el-table-column",{attrs:{label:"身份证号",align:"center","min-width":"150px"},scopedSlots:e._u([{key:"default",fn:function(t){return[n("span",[e._v(e._s(t.row.idCard))])]}}])}),e._v(" "),n("el-table-column",{attrs:{label:"薪资/月",prop:"salary",sortable:"custom",align:"center","min-width":"105px"},scopedSlots:e._u([{key:"default",fn:function(t){return[n("span",[e._v("￥ "+e._s(t.row.salary))])]}}])}),e._v(" "),n("el-table-column",{attrs:{label:"入职时间",prop:"entryTime",sortable:"custom","min-width":"135px",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[n("span",[e._v(e._s(e._f("parseTime")(t.row.entryTime,"{y}-{m}-{d}")))])]}}])}),e._v(" "),n("el-table-column",{attrs:{label:"状态","class-name":"status-col","min-width":"90px"},scopedSlots:e._u([{key:"default",fn:function(t){return[n("el-tag",{attrs:{type:e._f("statusFilter")(t.row.status)}},[e._v(e._s(e._f("typeFilter")(t.row.status)))])]}}])}),e._v(" "),n("el-table-column",{attrs:{label:"操作",align:"center","min-width":"100px","class-name":"small-padding fixed-width"},scopedSlots:e._u([{key:"default",fn:function(t){return[n("el-button",{attrs:{type:"primary",size:"mini"},on:{click:function(n){e.handleUpdate(t.row)}}},[e._v("编辑")])]}}])})],1),e._v(" "),n("pagination",{directives:[{name:"show",rawName:"v-show",value:e.total>0,expression:"total>0"}],attrs:{total:e.total,page:e.listQuery.page,limit:e.listQuery.limit},on:{"update:page":function(t){e.$set(e.listQuery,"page",t)},"update:limit":function(t){e.$set(e.listQuery,"limit",t)},pagination:e.getList}}),e._v(" "),n("el-dialog",{attrs:{visible:e.dialogFormVisible,title:"修改员工信息"},on:{"update:visible":function(t){e.dialogFormVisible=t}}},[n("el-form",{ref:"dataForm",staticStyle:{width:"400px","margin-left":"50px"},attrs:{rules:e.rules,model:e.temp,"label-position":"left","label-width":"100px"}},[n("el-form-item",{attrs:{label:"姓名",prop:"name"}},[n("el-input",{model:{value:e.temp.name,callback:function(t){e.$set(e.temp,"name",t)},expression:"temp.name"}})],1),e._v(" "),n("el-form-item",{attrs:{label:"联系电话",prop:"phone"}},[n("el-input",{model:{value:e.temp.phone,callback:function(t){e.$set(e.temp,"phone",t)},expression:"temp.phone"}})],1),e._v(" "),n("el-form-item",{attrs:{label:"身份证号",prop:"idCard"}},[n("el-input",{model:{value:e.temp.idCard,callback:function(t){e.$set(e.temp,"idCard",t)},expression:"temp.idCard"}})],1),e._v(" "),n("el-form-item",{attrs:{label:"性别",prop:"gender"}},[n("el-radio-group",{model:{value:e.temp.gender,callback:function(t){e.$set(e.temp,"gender",t)},expression:"temp.gender"}},[n("el-radio",{attrs:{label:"男"}}),e._v(" "),n("el-radio",{attrs:{label:"女"}})],1)],1),e._v(" "),n("el-form-item",{attrs:{label:"薪资/月",prop:"salary"}},[n("el-input",{model:{value:e.temp.salary,callback:function(t){e.$set(e.temp,"salary",e._n(t))},expression:"temp.salary"}})],1),e._v(" "),n("el-form-item",{attrs:{label:"状态",prop:"status"}},[n("el-select",{attrs:{placeholder:"请选择"},model:{value:e.temp.status,callback:function(t){e.$set(e.temp,"status",t)},expression:"temp.status"}},e._l(e.statusOptions,function(e){return n("el-option",{key:e.key,attrs:{label:e.display_name,value:e.key}})}))],1),e._v(" "),n("el-form-item",[n("el-button",{on:{click:function(t){e.dialogFormVisible=!1}}},[e._v("取消")]),e._v(" "),n("el-button",{attrs:{type:"primary"},on:{click:e.updateData}},[e._v("确认")])],1)],1)],1)],1)},[],!1,null,null,null);g.options.__file="message.vue";t.default=g.exports},ZySA:function(e,t,n){"use strict";var a=n("6ZY3"),i=n.n(a),r=(n("jUE0"),{bind:function(e,t){e.addEventListener("click",function(n){var a=i()({},t.value),r=i()({ele:e,type:"hit",color:"rgba(0, 0, 0, 0.15)"},a),l=r.ele;if(l){l.style.position="relative",l.style.overflow="hidden";var o=l.getBoundingClientRect(),s=l.querySelector(".waves-ripple");switch(s?s.className="waves-ripple":((s=document.createElement("span")).className="waves-ripple",s.style.height=s.style.width=Math.max(o.width,o.height)+"px",l.appendChild(s)),r.type){case"center":s.style.top=o.height/2-s.offsetHeight/2+"px",s.style.left=o.width/2-s.offsetWidth/2+"px";break;default:s.style.top=(n.pageY-o.top-s.offsetHeight/2-document.documentElement.scrollTop||document.body.scrollTop)+"px",s.style.left=(n.pageX-o.left-s.offsetWidth/2-document.documentElement.scrollLeft||document.body.scrollLeft)+"px"}return s.style.backgroundColor=r.color,s.className="waves-ripple z-active",!1}},!1)}}),l=function(e){e.directive("waves",r)};window.Vue&&(window.waves=r,Vue.use(l)),r.install=l;t.a=r},e9eg:function(e,t,n){e.exports={default:n("t3Qm"),__esModule:!0}},jUE0:function(e,t,n){},kmh5:function(e,t,n){var a=n("NCe+");a(a.S,"Number",{isInteger:n("Bfuw")})},t3Qm:function(e,t,n){n("kmh5"),e.exports=n("f4ri").Number.isInteger}}]);