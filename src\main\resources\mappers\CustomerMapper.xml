<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gxyan.dao.CustomerMapper">
  <resultMap id="BaseResultMap" type="com.gxyan.pojo.Customer">
    <constructor>
      <idArg column="id" javaType="java.lang.Long" jdbcType="BIGINT" />
      <arg column="name" javaType="java.lang.String" jdbcType="VARCHAR" />
      <arg column="phone" javaType="java.lang.String" jdbcType="VARCHAR" />
      <arg column="id_card" javaType="java.lang.String" jdbcType="VARCHAR" />
      <arg column="create_time" javaType="java.util.Date" jdbcType="TIMESTAMP" />
    </constructor>
  </resultMap>
  <sql id="Base_Column_List">
    id, name, phone, id_card, create_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from customer
    where id = #{id,jdbcType=BIGINT}
  </select>
  <select id="selectSelective" resultMap="BaseResultMap" parameterType="com.gxyan.vo.CustomerQuery">
    select
    <include refid="Base_Column_List"/>
    from customer
    <where>
      <choose>
        <when test="id != null and id != ''">
          id = #{id}
        </when>
        <when test="phone != null and phone != ''">
          phone = #{phone}
        </when>
        <when test="idCard != null and idCard != ''">
          id_card = #{idCard}
        </when>
        <when test="name != null and name != ''">
          <bind name="pattern" value="'%' + name + '%'"/>
          name like #{pattern}
        </when>
      </choose>
    </where>
    <if test="orderBy != null and orderBy != ''">
      order by ${orderBy}
    </if>
  </select>
  <select id="selectByIdCard" resultMap="BaseResultMap" parameterType="string">
    select
    <include refid="Base_Column_List"/>
    from customer
    where id_card = #{idCard}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from customer
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.gxyan.pojo.Customer">
    insert into customer (id, name, phone, 
      id_card, create_time)
    values (#{id,jdbcType=BIGINT}, #{name,jdbcType=VARCHAR}, #{phone,jdbcType=VARCHAR}, 
      #{idCard,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.gxyan.pojo.Customer">
    insert into customer
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="name != null">
        name,
      </if>
      <if test="phone != null">
        phone,
      </if>
      <if test="idCard != null">
        id_card,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="phone != null">
        #{phone,jdbcType=VARCHAR},
      </if>
      <if test="idCard != null">
        #{idCard,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.gxyan.pojo.Customer">
    update customer
    <set>
      <if test="name != null">
        name = #{name,jdbcType=VARCHAR},
      </if>
      <if test="phone != null">
        phone = #{phone,jdbcType=VARCHAR},
      </if>
      <if test="idCard != null">
        id_card = #{idCard,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.gxyan.pojo.Customer">
    update customer
    set name = #{name,jdbcType=VARCHAR},
      phone = #{phone,jdbcType=VARCHAR},
      id_card = #{idCard,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>